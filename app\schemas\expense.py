"""
Pydantic schemas for Expense-related API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from datetime import datetime
from typing import List, Optional, Dict
from decimal import Decimal
from app.schemas.user import UserResponse


class ExpenseSplitCreate(BaseModel):
    """Schema for creating an expense split."""
    user_id: int = Field(..., description="ID of user who owes this amount")
    amount_owed: Optional[Decimal] = Field(None, gt=0, description="Amount this user owes (for custom splits)")
    shares: Optional[int] = Field(None, gt=0, description="Number of shares for this user (for shares splits)")


class ExpenseSplitResponse(BaseModel):
    """Schema for expense split in responses."""
    id: int
    user_id: int
    amount_owed: Decimal
    shares: Optional[int] = None
    user: Optional[UserResponse] = None
    
    class Config:
        from_attributes = True


class ExpenseBase(BaseModel):
    """Base expense schema with common fields."""
    description: str = Field(..., min_length=1, max_length=200, description="What the expense was for")
    amount: Decimal = Field(..., gt=0, description="Total amount of the expense")
    category: Optional[str] = Field(None, max_length=50, description="Expense category")
    notes: Optional[str] = Field(None, description="Additional notes")


class ExpenseCreate(ExpenseBase):
    """Schema for creating a new expense."""
    group_id: Optional[int] = Field(None, description="ID of the group this expense belongs to (optional for direct expenses)")
    payer_id: Optional[int] = Field(None, description="ID of user who paid (defaults to current user)")
    
    # Split configuration
    split_type: str = Field("equal", description="Type of split: 'equal', 'custom', or 'shares'")
    participants: List[int] = Field(..., description="List of user IDs to split the expense with")
    custom_splits: Optional[List[ExpenseSplitCreate]] = Field(None, description="Custom split amounts/shares")

    @validator('split_type')
    def validate_split_type(cls, v):
        if v not in ['equal', 'custom', 'shares']:
            raise ValueError('split_type must be "equal", "custom", or "shares"')
        return v

    @validator('custom_splits')
    def validate_custom_splits(cls, v, values):
        split_type = values.get('split_type')
        if split_type in ['custom', 'shares'] and not v:
            raise ValueError(f'custom_splits is required when split_type is "{split_type}"')
        return v


class ExpenseUpdate(BaseModel):
    """Schema for updating an expense."""
    description: Optional[str] = Field(None, min_length=1, max_length=200)
    amount: Optional[Decimal] = Field(None, gt=0)
    category: Optional[str] = Field(None, max_length=50)
    notes: Optional[str] = None


class ExpenseResponse(ExpenseBase):
    """Schema for expense data in API responses."""
    id: int
    group_id: int
    payer_id: int
    created_at: datetime
    updated_at: datetime
    
    # Related data
    payer: Optional[UserResponse] = None
    splits: Optional[List[ExpenseSplitResponse]] = None
    
    class Config:
        from_attributes = True


class ExpenseListResponse(BaseModel):
    """Schema for paginated expense list responses."""
    expenses: List[ExpenseResponse]
    total: int
    page: int
    per_page: int
