#!/usr/bin/env python3
"""
Script to promote the admin user to admin status via direct database access.
"""

import requests
import json
import time

def promote_admin_user():
    """Promote admin user to admin status."""
    
    # Service URL
    service_url = "http://localhost:8080"
    
    try:
        # Wait for proxy to be ready
        print("⏳ Waiting for proxy connection...")
        time.sleep(3)
        
        # Test connection
        health_response = requests.get(f"{service_url}/health", timeout=10)
        if health_response.status_code != 200:
            print("❌ Service not accessible via proxy")
            return False
        
        print("✅ Connected to service via proxy")
        
        # Create a simple Python script to update the database
        update_script = '''
import sqlite3
import os

# Find database file
db_paths = [
    "/app/data/bhagbanda.db",
    "/app/bhagbanda.db", 
    "./bhagbanda.db",
    "bhagbanda.db"
]

db_file = None
for path in db_paths:
    if os.path.exists(path):
        db_file = path
        break

if db_file:
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Check current status
        cursor.execute("SELECT username, is_admin FROM users WHERE username = 'admin'")
        user = cursor.fetchone()
        
        if user:
            username, is_admin = user
            if is_admin:
                print(f"✅ User {username} is already admin")
            else:
                # Promote to admin
                cursor.execute("UPDATE users SET is_admin = 1 WHERE username = 'admin'")
                conn.commit()
                print(f"✅ User {username} promoted to admin!")
        else:
            print("❌ Admin user not found")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")
else:
    print("❌ Database file not found")
    print("Checked paths:", db_paths)
'''
        
        # Since we can't directly execute in the container, let's provide instructions
        print("🔧 Admin user promotion needed!")
        print("\n📋 To complete admin setup:")
        print("1. Go to: https://console.cloud.google.com")
        print("2. Navigate to: Cloud Run → bhagbanda-clean")
        print("3. Click: 'Cloud Shell' (terminal icon)")
        print("4. Run these commands:")
        print("\n```bash")
        print("# Connect to the running container")
        print("gcloud run services proxy bhagbanda-clean --region=us-central1 --port=8080 &")
        print("")
        print("# Create and run the admin promotion script")
        print("cat > promote_admin.py << 'EOF'")
        print(update_script.strip())
        print("EOF")
        print("")
        print("python3 promote_admin.py")
        print("```")
        print("\n✅ After running these commands, your admin user will have full privileges!")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        print("💡 Make sure the proxy is running: gcloud run services proxy bhagbanda-clean --region=us-central1 --port=8080")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Setting up admin privileges...")
    promote_admin_user()
