"""
Database configuration and session management.
Uses SQLAlchemy 2.0 with async support for better performance.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# Create database engine
# Note: For production, switch to PostgreSQL by changing the database_url
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all database models
Base = declarative_base()


def get_db():
    """
    Dependency function to get database session.
    This will be used with FastAPI's dependency injection system.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_default_admin():
    """
    Create default admin user with full privileges if no admin users exist.
    This ensures the admin user has complete access to:
    - User Management (view, activate/deactivate, delete, promote users)
    - Admin Dashboard (system statistics, health monitoring)
    - Database Management (backup, reset, maintenance)
    - System Administration (configuration, security monitoring)
    """
    try:
        import sqlite3
        import os
        from datetime import datetime, timezone
        from app.auth import get_password_hash

        # Get database path from settings
        db_url = settings.database_url
        if "sqlite" in db_url:
            # Handle different SQLite URL formats
            db_path = db_url.replace("sqlite:///", "").replace("./", "")

            # For container deployment, also check data directory
            possible_paths = [
                db_path,
                f"./data/{os.path.basename(db_path)}",
                f"/app/data/{os.path.basename(db_path)}",
                f"/app/{os.path.basename(db_path)}"
            ]

            actual_db_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    actual_db_path = path
                    break

            # If no existing database, use the primary path
            if not actual_db_path:
                actual_db_path = db_path
                # Ensure directory exists
                os.makedirs(os.path.dirname(actual_db_path), exist_ok=True)

            print(f"📁 Using database: {actual_db_path}")

            conn = sqlite3.connect(actual_db_path)
            cursor = conn.cursor()

            # Check if users table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if not cursor.fetchone():
                print("⚠️  Users table not found, will be created by SQLAlchemy")
                conn.close()
                return

            # Check if any admin users exist
            cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = 1")
            admin_count = cursor.fetchone()[0]

            if admin_count == 0:
                print("🔧 No admin users found, creating default admin...")

                # Check if admin user exists but is not admin
                cursor.execute("SELECT id, username, email FROM users WHERE username = 'admin'")
                existing_admin = cursor.fetchone()

                if existing_admin:
                    # Promote existing admin user to full admin privileges
                    cursor.execute("""
                        UPDATE users
                        SET is_admin = 1,
                            is_active = 1,
                            updated_at = datetime('now')
                        WHERE username = 'admin'
                    """)
                    conn.commit()
                    print("✅ Existing 'admin' user promoted to FULL ADMIN PRIVILEGES")
                    print("👑 Admin can now access:")
                    print("   • User Management Dashboard")
                    print("   • System Administration Panel")
                    print("   • Database Management Tools")
                    print("   • Security & Monitoring Features")
                else:
                    # Create new admin user with full privileges
                    password_hash = get_password_hash("admin6061")
                    current_time = datetime.now(timezone.utc).isoformat()

                    cursor.execute("""
                        INSERT INTO users (
                            username, email, password_hash, full_name,
                            is_admin, is_active, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        "admin",
                        "<EMAIL>",
                        password_hash,
                        "System Administrator",
                        1,  # is_admin = True (full admin privileges)
                        1,  # is_active = True
                        current_time,
                        current_time
                    ))
                    conn.commit()

                    print("✅ DEFAULT ADMIN USER CREATED WITH FULL PRIVILEGES")
                    print("👤 Login Credentials:")
                    print("   Username: admin")
                    print("   Password: admin6061")
                    print("👑 Admin Privileges Include:")
                    print("   • Complete User Management")
                    print("   • System Dashboard Access")
                    print("   • Database Administration")
                    print("   • Security & Configuration Control")
                    print("   • All Administrative Functions")
            else:
                print(f"ℹ️  Admin user already exists ({admin_count} admin(s) found)")

                # Verify admin user has correct privileges
                cursor.execute("SELECT username, is_admin, is_active FROM users WHERE username = 'admin'")
                admin_user = cursor.fetchone()
                if admin_user:
                    username, is_admin, is_active = admin_user
                    if is_admin and is_active:
                        print(f"✅ Admin user '{username}' has full privileges")
                    else:
                        print(f"⚠️  Admin user '{username}' needs privilege update")

            conn.close()

    except Exception as e:
        print(f"❌ Error creating default admin user: {e}")
        import traceback
        traceback.print_exc()


def create_tables():
    """Create all database tables. Called during app startup."""
    Base.metadata.create_all(bind=engine)

    # Create default admin user if no users exist
    create_default_admin()
