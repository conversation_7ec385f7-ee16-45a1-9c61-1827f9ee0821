"""
Database configuration and session management.
Uses SQLAlchemy 2.0 with async support for better performance.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# Create database engine
# Note: For production, switch to PostgreSQL by changing the database_url
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all database models
Base = declarative_base()


def get_db():
    """
    Dependency function to get database session.
    This will be used with FastAPI's dependency injection system.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_default_admin():
    """Create default admin user if no users exist."""
    try:
        from app.models.user import User
        from app.auth import get_password_hash

        db = SessionLocal()

        # Check if any users exist
        user_count = db.query(User).count()

        if user_count == 0:
            # Create default admin user
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                password_hash=get_password_hash("admin6061"),
                full_name="System Administrator",
                is_admin=True,
                is_active=True
            )

            db.add(admin_user)
            db.commit()
            print("✅ Default admin user created: username='admin', password='admin6061'")

        db.close()

    except Exception as e:
        print(f"❌ Error creating default admin user: {e}")


def create_tables():
    """Create all database tables. Called during app startup."""
    Base.metadata.create_all(bind=engine)

    # Create default admin user if no users exist
    create_default_admin()
