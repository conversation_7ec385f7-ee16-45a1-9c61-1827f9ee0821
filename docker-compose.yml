version: '3.8'

services:
  bhagbanda:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/bhagbanda.db
      - SECRET_KEY=change-this-secret-key-in-production-use-random-32-chars
      - DEBUG=false
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440
    volumes:
      # Persist database data
      - bhagbanda_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  bhagbanda_data:
