# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
/.venv
venv/
env/
ENV/

# Database files (contain sensitive data)
*.db
*.sqlite
*.sqlite3
bhagbanda.db

# Environment files (contain secrets)
.env
.env.local
.env.development
.env.test
.env.production

# Logs (may contain sensitive info)
*.log
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Build artifacts
build/
dist/
*.egg-info/

# Test coverage
.coverage
htmlcov/

# Temporary files
tmp/
temp/

# Docker volumes
data/

# Backup files
*.bak
*.backup

# Admin scripts with credentials (if any)
*_credentials.py
*_secrets.py