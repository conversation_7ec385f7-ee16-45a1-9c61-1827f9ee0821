#!/usr/bin/env python3
"""
Setup script to configure email for Bhagbanda app.
This script will help you set up Gmail for sending emails.
"""

import os
import sys

def main():
    print("🚀 Bhagbanda Email Setup")
    print("=" * 50)
    print()
    
    print("To enable email notifications, you need to set up Gmail:")
    print()
    
    print("📧 Step 1: Create Gmail Account")
    print("- Go to https://accounts.google.com/signup")
    print("- Create account: <EMAIL> (or similar)")
    print("- Complete account setup")
    print()
    
    print("🔐 Step 2: Enable 2-Factor Authentication")
    print("- Go to https://myaccount.google.com/security")
    print("- Turn on 2-Step Verification")
    print("- Follow the setup process")
    print()
    
    print("🔑 Step 3: Generate App Password")
    print("- Go to https://myaccount.google.com/apppasswords")
    print("- Select 'Mail' as the app")
    print("- Generate password (16 characters)")
    print("- Copy the password (no spaces)")
    print()
    
    print("⚙️  Step 4: Set Environment Variables")
    print("For local testing:")
    print("export SMTP_USERNAME='<EMAIL>'")
    print("export SMTP_PASSWORD='your-16-char-app-password'")
    print()
    
    print("For Google Cloud deployment:")
    print("gcloud run deploy bhagbanda-admin \\")
    print("  --image=your-image \\")
    print("  --set-env-vars='SMTP_USERNAME=<EMAIL>,SMTP_PASSWORD=your-app-password'")
    print()
    
    print("🧪 Step 5: Test Email")
    print("python test_email.py")
    print()
    
    print("✅ Once configured, the app will send emails for:")
    print("- Group member invitations")
    print("- Password reset links")
    print()
    
    # Interactive setup
    print("🔧 Interactive Setup")
    print("-" * 20)
    
    username = input("Enter Gmail username (e.g., <EMAIL>): ").strip()
    if not username:
        print("❌ Username is required")
        return
    
    password = input("Enter App Password (16 characters, no spaces): ").strip()
    if not password:
        print("❌ App Password is required")
        return
    
    if len(password) != 16:
        print("⚠️  Warning: App Password should be 16 characters")
    
    print()
    print("🚀 Configuration Summary:")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    # Generate environment variable commands
    print("📋 Environment Variable Commands:")
    print()
    print("For local testing:")
    print(f"export SMTP_USERNAME='{username}'")
    print(f"export SMTP_PASSWORD='{password}'")
    print()
    
    print("For Google Cloud deployment:")
    env_vars = f"SMTP_USERNAME={username},SMTP_PASSWORD={password}"
    print("gcloud run deploy bhagbanda-admin \\")
    print("  --image=ssshakya/bhagbanda:latest \\")
    print(f"  --set-env-vars='{env_vars}'")
    print()
    
    # Create .env file
    env_content = f"""# Bhagbanda Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME={username}
SMTP_PASSWORD={password}
SENDER_EMAIL=<EMAIL>
SENDER_NAME=Bhagbanda
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with email configuration")
        print("   (Don't commit this file to version control!)")
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
    
    print()
    print("🧪 Next Steps:")
    print("1. Test the configuration: python test_email.py")
    print("2. If test passes, deploy with the environment variables")
    print("3. Test group invitations and password reset in the app")
    print()
    print("🆘 Troubleshooting:")
    print("- Make sure 2FA is enabled on Gmail")
    print("- Use App Password, not regular password")
    print("- Check that 'Less secure app access' is disabled")
    print("- Verify the 16-character App Password is correct")

if __name__ == "__main__":
    main()
