#!/usr/bin/env python3
"""
Test script to verify email configuration works.
"""

import os
import sys
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart

def test_email_config():
    """Test email configuration with actual SMTP server."""
    
    # Email configuration
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    sender_email = "<EMAIL>"  # Test account
    sender_name = "Bhagbanda Test"
    
    # Test credentials (you'll need to update these)
    smtp_username = "<EMAIL>"
    smtp_password = "test-app-password"  # 16-character app password
    
    # Test recipient
    test_recipient = "<EMAIL>"  # Change to your email for testing
    
    print("🧪 Testing email configuration...")
    print(f"SMTP Server: {smtp_server}:{smtp_port}")
    print(f"Sender: {sender_email}")
    print(f"Recipient: {test_recipient}")
    
    try:
        # Create test message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = "Bhagbanda Email Test"
        msg["From"] = f"{sender_name} <{sender_email}>"
        msg["To"] = test_recipient
        
        # Create HTML content
        html_body = """
        <html>
        <body>
            <h2>🎉 Email Configuration Test</h2>
            <p>If you're reading this, the Bhagbanda email configuration is working!</p>
            <p>This means:</p>
            <ul>
                <li>✅ SMTP connection successful</li>
                <li>✅ Authentication working</li>
                <li>✅ Email sending functional</li>
            </ul>
            <p>You can now enable email notifications for:</p>
            <ul>
                <li>📧 Group invitations</li>
                <li>🔐 Password reset links</li>
            </ul>
            <p>Best regards,<br>The Bhagbanda Team</p>
        </body>
        </html>
        """
        
        # Create text content
        text_body = """
        Email Configuration Test
        
        If you're reading this, the Bhagbanda email configuration is working!
        
        This means:
        - SMTP connection successful
        - Authentication working
        - Email sending functional
        
        You can now enable email notifications for:
        - Group invitations
        - Password reset links
        
        Best regards,
        The Bhagbanda Team
        """
        
        # Attach parts
        text_part = MIMEText(text_body, "plain")
        html_part = MIMEText(html_body, "html")
        msg.attach(text_part)
        msg.attach(html_part)
        
        # Send email
        print("📤 Sending test email...")
        context = ssl.create_default_context()
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(smtp_username, smtp_password)
            server.sendmail(sender_email, test_recipient, msg.as_string())
        
        print("✅ Email sent successfully!")
        print(f"Check {test_recipient} for the test email.")
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {str(e)}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure you're using an App Password, not your regular Gmail password")
        print("2. Enable 2-Factor Authentication on your Gmail account")
        print("3. Generate a new App Password in Google Account settings")
        print("4. Check that 'Less secure app access' is disabled")
        print("5. Verify the email credentials are correct")
        return False

if __name__ == "__main__":
    print("🚀 Bhagbanda Email Configuration Test")
    print("=" * 50)
    
    success = test_email_config()
    
    if success:
        print("\n🎉 Email configuration is working!")
        print("You can now deploy with email notifications enabled.")
    else:
        print("\n❌ Email configuration needs to be fixed.")
        print("Please update the credentials and try again.")
    
    sys.exit(0 if success else 1)
