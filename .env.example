# Example environment variables file
# Copy this to .env and update the values

# Database Configuration
DATABASE_URL=sqlite:///./bhagbanda.db
# For PostgreSQL: DATABASE_URL=postgresql://username:password@localhost/bhagbanda

# JWT Configuration (IMPORTANT: Change these in production!)
SECRET_KEY=change-this-to-a-random-32-character-secret-key-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Admin Configuration
ADMIN_PASSWORD=change-this-admin-password-in-production

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=Bhagbanda Backend
VERSION=1.0.0
DESCRIPTION=Backend API for expense splitting application

# CORS Configuration (comma-separated list)
ALLOWED_ORIGINS=*

# Development Configuration
DEBUG=true
