"""
Admin Groups router for super admin management of all groups and expenses.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models.user import User
from app.models.group import Group, GroupMember
from app.models.expense import Expense, ExpenseSplit
from app.schemas.group import GroupResponse, GroupCreate, GroupUpdate
from app.schemas.expense import ExpenseResponse, ExpenseCreate, ExpenseUpdate
from app.auth import get_current_active_user

router = APIRouter(prefix="/admin/groups", tags=["Admin Groups"])


def require_admin(current_user: User = Depends(get_current_active_user)):
    """Require admin privileges for all admin endpoints."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user


@router.get("/", response_model=List[GroupResponse])
def get_all_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get all groups in the system (admin only).
    
    Allows admins to view and manage all groups regardless of membership.
    """
    query = db.query(Group)
    
    if search:
        query = query.filter(Group.name.ilike(f"%{search}%"))
    
    groups = query.offset(skip).limit(limit).all()
    
    # Add member count to each group
    group_responses = []
    for group in groups:
        member_count = db.query(GroupMember).filter(GroupMember.group_id == group.id).count()
        expense_count = db.query(Expense).filter(Expense.group_id == group.id).count()
        
        group_dict = {
            "id": group.id,
            "name": group.name,
            "description": group.description,
            "created_at": group.created_at,
            "created_by": group.created_by,
            "member_count": member_count,
            "expense_count": expense_count
        }
        group_responses.append(GroupResponse.model_validate(group_dict))
    
    return group_responses


@router.get("/{group_id}", response_model=GroupResponse)
def get_group_details(
    group_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about any group (admin only).
    """
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Get members
    members = db.query(GroupMember).filter(GroupMember.group_id == group_id).all()
    member_count = len(members)
    expense_count = db.query(Expense).filter(Expense.group_id == group_id).count()
    
    group_dict = {
        "id": group.id,
        "name": group.name,
        "description": group.description,
        "created_at": group.created_at,
        "created_by": group.created_by,
        "member_count": member_count,
        "expense_count": expense_count,
        "members": [{"user_id": m.user_id, "joined_at": m.joined_at} for m in members]
    }
    
    return GroupResponse.model_validate(group_dict)


@router.put("/{group_id}", response_model=GroupResponse)
def update_group(
    group_id: int,
    group_update: GroupUpdate,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update any group (admin only).
    """
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Update group fields
    if group_update.name is not None:
        group.name = group_update.name
    if group_update.description is not None:
        group.description = group_update.description
    
    db.commit()
    db.refresh(group)
    
    return GroupResponse.model_validate(group)


@router.delete("/{group_id}")
def delete_group(
    group_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete any group and all its expenses (admin only).
    
    This is a destructive operation that will:
    1. Delete all expenses in the group
    2. Delete all expense splits
    3. Delete all group memberships
    4. Delete the group itself
    """
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Delete all expenses and their splits first
    expenses = db.query(Expense).filter(Expense.group_id == group_id).all()
    for expense in expenses:
        # Delete expense splits
        db.query(ExpenseSplit).filter(ExpenseSplit.expense_id == expense.id).delete()
        # Delete expense
        db.delete(expense)
    
    # Delete group memberships
    db.query(GroupMember).filter(GroupMember.group_id == group_id).delete()
    
    # Delete the group
    db.delete(group)
    db.commit()
    
    return {"message": f"Group '{group.name}' and all its data have been deleted"}


@router.get("/{group_id}/expenses", response_model=List[ExpenseResponse])
def get_group_expenses(
    group_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get all expenses for any group (admin only).
    """
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    expenses = db.query(Expense).filter(
        Expense.group_id == group_id
    ).offset(skip).limit(limit).all()
    
    return [ExpenseResponse.model_validate(expense) for expense in expenses]


@router.post("/{group_id}/members/{user_id}")
def add_member_to_group(
    group_id: int,
    user_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add any user to any group (admin only).
    """
    # Validate group exists
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Validate user exists
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check if already a member
    existing_membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == user_id
    ).first()
    
    if existing_membership:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already a member of this group"
        )
    
    # Add membership
    membership = GroupMember(group_id=group_id, user_id=user_id)
    db.add(membership)
    db.commit()
    
    return {"message": f"User {user.username} added to group {group.name}"}


@router.delete("/{group_id}/members/{user_id}")
def remove_member_from_group(
    group_id: int,
    user_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Remove any user from any group (admin only).
    """
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == user_id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User is not a member of this group"
        )
    
    db.delete(membership)
    db.commit()
    
    return {"message": "User removed from group"}
