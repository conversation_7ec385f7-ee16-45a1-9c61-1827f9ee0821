"""
Authentication router for user registration, login, and token management.
"""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import secrets

from app.database import get_db
from app.models.user import User
from app.schemas.user import User<PERSON><PERSON>, UserResponse, UserLogin, Token
from app.auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_active_user
)
from app.config import settings
from app.services.email_service import email_service

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def register_user(user_data: UserCreate, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """
    Register a new user account.
    
    Creates a new user with hashed password and returns user information.
    Username and email must be unique.
    """
    # Check if username already exists
    if db.query(User).filter(User.username == user_data.username).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if email already exists
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Generate email verification token
    verification_token = secrets.token_urlsafe(32)

    # Create new user with hashed password (inactive until email verified)
    hashed_password = get_password_hash(user_data.password)

    # Create user with email verification if supported, otherwise create active user
    try:
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=hashed_password,
            full_name=user_data.full_name,
            is_active=False,  # User inactive until email verified
            is_email_verified=False,
            email_verification_token=verification_token
        )
    except TypeError:
        # Fallback for databases without email verification columns
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=hashed_password,
            full_name=user_data.full_name,
            is_active=True  # Active by default if email verification not supported
        )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Send verification email only if email verification is enabled
    if hasattr(db_user, 'email_verification_token') and db_user.email_verification_token:
        background_tasks.add_task(
            email_service.send_email_verification_email,
            user_email=db_user.email,
            user_name=db_user.full_name or db_user.username,
            verification_token=verification_token
        )

    return db_user


@router.post("/verify-email")
def verify_email(token: str, db: Session = Depends(get_db)):
    """
    Verify user email address using verification token.

    This endpoint activates the user account after email verification.
    """
    # Find user with this verification token
    user = db.query(User).filter(User.email_verification_token == token).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification token"
        )

    if user.is_email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already verified"
        )

    # Verify email and activate user
    user.is_email_verified = True
    user.is_active = True
    user.email_verification_token = None  # Clear the token

    db.commit()
    db.refresh(user)

    return {"message": "Email verified successfully. You can now log in."}


@router.post("/resend-verification")
def resend_verification_email(
    email: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Resend email verification for unverified users.
    """
    user = db.query(User).filter(User.email == email).first()

    if not user:
        # Don't reveal whether email exists
        return {"message": "If an account with that email exists and is unverified, a verification email has been sent."}

    if user.is_email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is already verified"
        )

    # Generate new verification token
    verification_token = secrets.token_urlsafe(32)
    user.email_verification_token = verification_token

    db.commit()

    # Send verification email
    background_tasks.add_task(
        email_service.send_email_verification_email,
        user_email=user.email,
        user_name=user.full_name or user.username,
        verification_token=verification_token
    )

    return {"message": "If an account with that email exists and is unverified, a verification email has been sent."}


@router.post("/login", response_model=Token)
def login_user(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """
    Authenticate user and return access token.
    
    Accepts username or email with password and returns JWT token.
    """
    user = authenticate_user(db, user_credentials.username, user_credentials.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username/email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user account is active (email verified)
    # Handle backward compatibility for users without email verification fields
    try:
        is_active = getattr(user, 'is_active', True)  # Default to True for backward compatibility
        is_email_verified = getattr(user, 'is_email_verified', True)  # Default to True for existing users

        if not is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Account not activated. Please verify your email address before signing in."
            )
    except AttributeError:
        # If email verification fields don't exist, allow login (backward compatibility)
        pass
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60  # Convert to seconds
    }


@router.get("/me", response_model=UserResponse)
def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current authenticated user's information.
    
    This endpoint can be used to verify token validity and get user details.
    """
    return current_user


@router.post("/refresh", response_model=Token)
def refresh_token(current_user: User = Depends(get_current_active_user)):
    """
    Refresh the access token for the current user.
    
    Returns a new token with extended expiration time.
    """
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": current_user.username, "user_id": current_user.id},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60
    }



