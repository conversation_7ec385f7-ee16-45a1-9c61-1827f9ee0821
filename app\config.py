"""
Configuration settings for the Bhagbanda backend application.
Uses Pydantic Settings for environment variable management.
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Database settings
    database_url: str = "sqlite:///./bhagbanda.db"

    # PostgreSQL settings for production
    postgres_host: Optional[str] = None
    postgres_port: int = 5432
    postgres_db: str = "bhagbanda"
    postgres_user: Optional[str] = None
    postgres_password: Optional[str] = None
    
    # JWT settings
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # API settings
    api_v1_prefix: str = "/api/v1"
    project_name: str = "Bhagbanda Backend"
    version: str = "1.0.0"
    description: str = "Backend API for expense splitting application"
    
    # CORS settings - parse from environment variable or use defaults
    allowed_origins: list[str] = ["http://localhost:3000", "http://localhost:5173"]
    
    # Development settings
    debug: bool = True

    # Admin settings
    admin_password: str = "change-this-admin-password-in-production"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Auto-configure PostgreSQL database URL if PostgreSQL settings are provided
        if self.postgres_host and self.postgres_user and self.postgres_password:
            self.database_url = f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
            print(f"🐘 Using PostgreSQL database: {self.postgres_host}:{self.postgres_port}/{self.postgres_db}")
        else:
            print(f"📁 Using SQLite database: {self.database_url}")

        # Parse CORS origins from environment variable if provided
        cors_origins_env = os.getenv('CORS_ORIGINS')
        if cors_origins_env:
            try:
                # Handle both JSON array format and comma-separated format
                if cors_origins_env.startswith('['):
                    import json
                    self.allowed_origins = json.loads(cors_origins_env)
                else:
                    self.allowed_origins = [origin.strip() for origin in cors_origins_env.split(',')]
            except (json.JSONDecodeError, ValueError):
                # Fallback to default if parsing fails
                pass


# Global settings instance
settings = Settings()
