/**
 * Group Detail Page Component
 * Shows detailed view of a specific group with expenses and members
 */

import React, { useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ArrowLeft,
  Plus,
  Users,
  Receipt,
  DollarSign,
  Calendar,
  User,
  CheckCircle
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { groupService } from '../services/groupService';
import { expenseService } from '../services/expenseService';
import { balanceService } from '../services/balanceService';
import { CreateExpenseModal } from '../components/expenses/CreateExpenseModal';
import { AddMemberModal } from '../components/groups/AddMemberModal';
import { SettleUpButton } from '../components/settlements/SettleUpButton';

export const GroupDetailPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const [isCreateExpenseModalOpen, setIsCreateExpenseModalOpen] = useState(false);
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const groupIdNum = parseInt(groupId || '0');

  // Fetch group details
  const { data: group, isLoading: groupLoading } = useQuery({
    queryKey: ['group', groupIdNum],
    queryFn: () => groupService.getGroup(groupIdNum),
    enabled: !!groupIdNum,
  });

  // Fetch group expenses
  const { data: expenses = [], isLoading: expensesLoading } = useQuery({
    queryKey: ['expenses', groupIdNum],
    queryFn: () => expenseService.getExpenses(groupIdNum),
    enabled: !!groupIdNum,
  });

  // Fetch group balance
  const { data: balance, isLoading: balanceLoading, refetch: refetchBalance } = useQuery({
    queryKey: ['group-balance', groupIdNum],
    queryFn: () => balanceService.getGroupBalance(groupIdNum),
    enabled: !!groupIdNum,
    staleTime: 0, // Always consider data stale
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  // Fetch settlement suggestions for this group
  const { data: settlements = [], isLoading: settlementsLoading, refetch: refetchSettlements } = useQuery({
    queryKey: ['settlement-suggestions', groupIdNum],
    queryFn: () => balanceService.getSettlementSuggestions(groupIdNum),
    enabled: !!groupIdNum,
    staleTime: 0, // Always consider data stale
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  // Create expense mutation
  const createExpenseMutation = useMutation({
    mutationFn: expenseService.createExpense,
    onSuccess: () => {
      // Invalidate all balance and expense related queries
      queryClient.invalidateQueries({ queryKey: ['expenses', groupIdNum] });
      queryClient.invalidateQueries({ queryKey: ['group-balance', groupIdNum] });
      queryClient.invalidateQueries({ queryKey: ['settlement-suggestions', groupIdNum] });
      queryClient.invalidateQueries({ queryKey: ['overall-balance'] });
      queryClient.invalidateQueries({ queryKey: ['all-expenses'] });

      // Force immediate refetch of balance data
      queryClient.refetchQueries({ queryKey: ['group-balance', groupIdNum] });
      queryClient.refetchQueries({ queryKey: ['settlement-suggestions', groupIdNum] });
      queryClient.refetchQueries({ queryKey: ['overall-balance'] });

      // Also manually refetch the local queries
      refetchBalance();
      refetchSettlements();

      toast.success('Expense created successfully!');
      setIsCreateExpenseModalOpen(false);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Add member mutation
  const addMemberMutation = useMutation({
    mutationFn: (userId: number) => groupService.addMember(groupIdNum, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group', groupIdNum] });
      toast.success('Member added successfully!');
      setIsAddMemberModalOpen(false);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (groupLoading || expensesLoading || balanceLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Group not found</h3>
        <Link to="/groups" className="text-primary-600 hover:text-primary-500">
          Back to Groups
        </Link>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link
            to="/groups"
            className="mr-4 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
            {group.description && (
              <p className="mt-1 text-sm text-gray-600">{group.description}</p>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-1" />
              {group.member_count || 0} members
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              Created {formatDate(group.created_at)}
            </div>
          </div>
          <button
            onClick={() => setIsCreateExpenseModalOpen(true)}
            className="btn-primary w-full sm:w-auto justify-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Expense
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Recent Expenses */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Recent Expenses</h3>
            </div>
            <div className="card-body">
              {expenses.length === 0 ? (
                <div className="text-center py-6">
                  <Receipt className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No expenses yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Start by adding your first expense to this group.
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={() => setIsCreateExpenseModalOpen(true)}
                      className="btn-primary"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Expense
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {expenses.map((expense) => (
                    <div key={expense.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900">{expense.description}</h4>
                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            Paid by {expense.payer?.full_name || expense.payer?.username}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDate(expense.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-medium text-gray-900">
                          {formatCurrency(expense.amount)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {expense.splits?.length || 0} people
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Balance Summary */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Your Balance</h3>
            </div>
            <div className="card-body">
              {balance ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">You are owed:</span>
                    <span className="text-lg font-medium text-success-600">
                      {formatCurrency(balance.total_owed_to_you)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">You owe:</span>
                    <span className="text-lg font-medium text-danger-600">
                      {formatCurrency(balance.total_you_owe)}
                    </span>
                  </div>
                  <hr />
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">Net balance:</span>
                    <span className={`text-lg font-bold ${
                      (balance.total_owed_to_you - balance.total_you_owe) >= 0 
                        ? 'text-success-600' 
                        : 'text-danger-600'
                    }`}>
                      {formatCurrency(balance.total_owed_to_you - balance.total_you_owe)}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No balance data available</p>
              )}
            </div>
          </div>

          {/* Settlement Suggestions */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Settle Up</h3>
            </div>
            <div className="card-body">
              {settlementsLoading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                </div>
              ) : settlements.length > 0 ? (
                <div className="space-y-3">
                  {settlements.map((settlement, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {settlement.from_user.full_name || settlement.from_user.username}
                        </p>
                        <p className="text-xs text-gray-500">
                          owes {settlement.to_user.full_name || settlement.to_user.username}
                        </p>
                        <p className="text-sm font-semibold text-primary-600">
                          {formatCurrency(settlement.amount)}
                        </p>
                      </div>
                      <SettleUpButton
                        suggestion={settlement}
                        groupId={groupIdNum}
                        variant="compact"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">All Settled Up! 🎉</h3>
                  <p className="text-sm text-gray-500">
                    No outstanding balances in this group. Add new expenses to split more costs.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Group Members */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Members</h3>
                <button
                  onClick={() => setIsAddMemberModalOpen(true)}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Member
                </button>
              </div>
            </div>
            <div className="card-body">
              {group.members && group.members.length > 0 ? (
                <div className="space-y-3">
                  {group.members.map((member) => (
                    <div key={member.user.id} className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {member.user.full_name || member.user.username}
                        </p>
                        <p className="text-xs text-gray-500">
                          Joined {formatDate(member.joined_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No members found</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create Expense Modal */}
      <CreateExpenseModal
        isOpen={isCreateExpenseModalOpen}
        onClose={() => setIsCreateExpenseModalOpen(false)}
        onSubmit={(data) => createExpenseMutation.mutate(data)}
        isLoading={createExpenseMutation.isPending}
        groupId={groupIdNum}
        groupMembers={group.members || []}
      />

      {/* Add Member Modal */}
      <AddMemberModal
        isOpen={isAddMemberModalOpen}
        onClose={() => setIsAddMemberModalOpen(false)}
        onAddMember={(userId) => addMemberMutation.mutate(userId)}
        isLoading={addMemberMutation.isPending}
        existingMemberIds={group.members?.map(m => m.user.id) || []}
      />
    </div>
  );
};
