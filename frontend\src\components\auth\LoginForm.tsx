/**
 * Login Form Component
 * Handles user login with form validation and error handling
 */

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Eye, EyeOff, LogIn, Users, DollarSign } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { authService } from '../../services/authService';
import { LoginRequest } from '../../types/api';
import { handleApiError, tokenManager } from '../../lib/api';

export const LoginForm: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginRequest>();

  const onSubmit = async (data: LoginRequest) => {
    console.log('🚀 Login form submitted with:', { username: data.username, password: '***' });
    setIsLoading(true);

    try {
      // Step 1: Direct API call to login
      console.log('📡 Step 1: Direct API login call...');
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Login failed');
      }

      const authResponse = await response.json();
      console.log('🎫 Step 1: Login response received:', authResponse);

      // Step 2: Store token
      console.log('💾 Step 2: Storing token...');
      tokenManager.setToken(authResponse.access_token);

      // Step 3: Get user data
      console.log('👤 Step 3: Fetching user data...');
      const userResponse = await fetch('/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${authResponse.access_token}`
        }
      });

      if (!userResponse.ok) {
        throw new Error('Failed to get user data');
      }

      const user = await userResponse.json();
      console.log('✅ Step 3: User data received:', user);

      // Step 4: Update auth store directly
      console.log('🔄 Step 4: Updating auth store...');
      const authStore = useAuthStore.getState();
      authStore.setUser(user);
      authStore.setLoading(false);
      console.log('✅ Step 4: Auth store updated');

      toast.success(`Welcome back, ${user.full_name || user.username}!`);

      // Step 5: Force redirect
      console.log('🚀 Step 5: Redirecting to dashboard...');
      window.location.href = '/dashboard';

    } catch (error) {
      console.error('❌ Login error:', error);

      // Check if it's an email verification issue
      if (error.message.includes('inactive') || error.message.includes('verify')) {
        toast.error('Please verify your email address before signing in.');
      } else {
        toast.error(`Login failed: ${error.message}`);
      }

      tokenManager.removeToken();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          {/* Bhagbanda Logo/Branding */}
          <div className="flex justify-center mb-6">
            <div className="flex items-center space-x-2">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Bhagbanda</h1>
                <p className="text-sm text-gray-500">Split expenses with friends</p>
              </div>
            </div>
          </div>

          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Welcome back!
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to manage your shared expenses
          </p>
          <p className="mt-1 text-center text-sm text-gray-600">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              create a new account
            </Link>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            {/* Username/Email Field */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                Username or Email
              </label>
              <input
                {...register('username', { 
                  required: 'Username or email is required' 
                })}
                type="text"
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="Enter your username or email"
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  {...register('password', { 
                    required: 'Password is required' 
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  Sign in
                </>
              )}
            </button>
          </div>

          <div className="text-center space-y-2">
            <Link
              to="/forgot-password"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              Forgot your password?
            </Link>
            <Link
              to="/resend-verification"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              Resend verification email
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};
