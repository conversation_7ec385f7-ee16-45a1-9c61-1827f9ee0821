import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { Plus, Receipt, Users, TrendingUp, TrendingDown } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import { groupService } from '../services/groupService';
import { balanceService } from '../services/balanceService';

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore();

  // Test: Add groups and balance queries
  const { data: groups = [], isLoading: groupsLoading } = useQuery({
    queryKey: ['groups'],
    queryFn: async () => {
      try {
        return await groupService.getMyGroups();
      } catch (error) {
        console.error('Error fetching groups:', error);
        return [];
      }
    },
  });

  const { data: overallBalance } = useQuery({
    queryKey: ['overall-balance'],
    queryFn: async () => {
      try {
        const balance = await balanceService.getOverallBalance();
        return balance || { net_balance: 0, total_you_owe: 0, total_owed_to_you: 0, group_balances: [] };
      } catch (error) {
        console.error('Error fetching overall balance:', error);
        return { net_balance: 0, total_you_owe: 0, total_owed_to_you: 0, group_balances: [] };
      }
    },
  });



  const formatCurrency = (amount: number | undefined | null) => {
    try {
      const safeAmount = typeof amount === 'number' ? amount : 0;
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(safeAmount);
    } catch (error) {
      return '$0.00';
    }
  };



  if (!user) {
    return (
      <div className="p-8">
        <p>Loading user information...</p>
      </div>
    );
  }

  if (groupsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        <p className="ml-3 text-gray-600">Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.full_name || user?.username}!
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Here's an overview of your expenses and balances.
            </p>
          </div>

          {/* Quick Action Buttons */}
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <Link
              to="/groups?create=true"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Link>
            <Link
              to="/expenses"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Receipt className="h-4 w-4 mr-2" />
              View Expenses
            </Link>
          </div>
        </div>
      </div>

      {/* Quick Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Receipt className="h-5 w-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              💡 Feature: Add Expenses with Friends
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              You can add expenses directly with friends without creating a group!
              Go to "View Expenses" and use the "Add Expense" feature.
            </p>
          </div>
        </div>
      </div>

      {/* Simple Stats */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Dashboard</h3>
        <p className="text-gray-600 mb-4">
          Your expense tracking dashboard is working! All features are available through the navigation menu.
        </p>

        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <div className="flex items-center">
              <Users className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-sm font-medium">Groups ({groups?.length || 0})</span>
            </div>
            <Link to="/groups" className="text-primary-600 hover:text-primary-500 text-sm">
              Manage Groups →
            </Link>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-sm font-medium">Expenses</span>
            <Link to="/expenses" className="text-primary-600 hover:text-primary-500 text-sm">
              View All Expenses →
            </Link>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-sm font-medium">Owed to You: {formatCurrency(overallBalance?.total_owed_to_you || 0)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <div className="flex items-center">
              <TrendingDown className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-sm font-medium">You Owe: {formatCurrency(overallBalance?.total_you_owe || 0)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-sm font-medium">View All Balances</span>
            <Link to="/balances" className="text-primary-600 hover:text-primary-500 text-sm">
              Check Balances →
            </Link>
          </div>
        </div>
      </div>


    </div>
  );
};
