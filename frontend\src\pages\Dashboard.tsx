import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { Plus, Users, Receipt, TrendingUp, TrendingDown } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { groupService } from '../services/groupService';
import { balanceService } from '../services/balanceService';
import { expenseService } from '../services/expenseService';
import { useAuthStore } from '../stores/authStore';
import { CreateExpenseModal } from '../components/expenses/CreateExpenseModal';

export const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const [showExpenseModal, setShowExpenseModal] = useState(false);

  // ALL HOOKS MUST BE CALLED BEFORE ANY EARLY RETURNS

  // Listen for expense modal open event
  useEffect(() => {
    const handleOpenExpenseModal = () => {
      setShowExpenseModal(true);
    };

    window.addEventListener('openExpenseModal', handleOpenExpenseModal);
    return () => {
      window.removeEventListener('openExpenseModal', handleOpenExpenseModal);
    };
  }, []);

  // Fetch user's groups with error handling
  const { data: groups = [], isLoading: groupsLoading } = useQuery({
    queryKey: ['groups'],
    queryFn: async () => {
      try {
        return await groupService.getMyGroups();
      } catch (error) {
        console.error('Error fetching groups:', error);
        return [];
      }
    },
  });

  // Fetch overall balance with error handling
  const { data: overallBalance } = useQuery({
    queryKey: ['overall-balance'],
    queryFn: async () => {
      try {
        const balance = await balanceService.getOverallBalance();
        return balance || { net_balance: 0, total_you_owe: 0, total_owed_to_you: 0, group_balances: [] };
      } catch (error) {
        console.error('Error fetching overall balance:', error);
        return { net_balance: 0, total_you_owe: 0, total_owed_to_you: 0, group_balances: [] };
      }
    },
  });

  // Fetch recent expenses with error handling
  const { data: recentExpenses = [] } = useQuery({
    queryKey: ['recent-expenses', groups?.length || 0],
    queryFn: async () => {
      if (!groups || groups.length === 0) return [];

      try {
        const expensePromises = groups.slice(0, 3).map(group =>
          expenseService.getExpenses(group.id).catch(() => [])
        );

        const allExpenses = await Promise.all(expensePromises);
        return allExpenses
          .flat()
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);
      } catch (error) {
        console.error('Error fetching recent expenses:', error);
        return [];
      }
    },
    enabled: true,
  });

  // Helper functions
  const formatCurrency = (amount: number | undefined | null) => {
    try {
      const safeAmount = typeof amount === 'number' ? amount : 0;
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(safeAmount);
    } catch (error) {
      return '$0.00';
    }
  };

  const formatDate = (dateString: string | undefined | null) => {
    try {
      if (!dateString) return 'Unknown date';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    } catch (error) {
      return 'Unknown date';
    }
  };

  const handleCreateExpense = async (expenseData: any) => {
    try {
      if (expenseData.group_id) {
        await expenseService.createExpense(expenseData.group_id, expenseData);
      } else {
        await expenseService.createDirectExpense(expenseData);
      }

      toast.success('Expense created successfully!');
      setShowExpenseModal(false);

      queryClient.invalidateQueries({ queryKey: ['recent-expenses'] });
      queryClient.invalidateQueries({ queryKey: ['overall-balance'] });
      queryClient.invalidateQueries({ queryKey: ['all-expenses'] });
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to create expense');
    }
  };

  // NOW EARLY RETURNS AFTER ALL HOOKS
  if (!user) {
    return (
      <div className="p-8">
        <p>Loading user information...</p>
      </div>
    );
  }

  if (groupsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        <p className="ml-3 text-gray-600">Loading dashboard...</p>
      </div>
    );
  }

  // MAIN RENDER - FULL DASHBOARD
  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.full_name || user?.username}!
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Here's an overview of your expenses and balances.
            </p>
          </div>

          {/* Quick Action Buttons */}
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <Link
              to="/groups?create=true"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Link>
            <button
              onClick={() => setShowExpenseModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Receipt className="h-4 w-4 mr-2" />
              Add Expense
            </button>
          </div>
        </div>
      </div>

      {/* Quick Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Receipt className="h-5 w-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              💡 New Feature: Add Expenses with Friends
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              You can now add expenses directly with friends without creating a group!
              Click "Add Expense" and choose "Direct Expense" to split bills with specific friends instantly.
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
        {/* Total Groups */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Groups
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {groups?.length || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Amount Owed to You */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-6 w-6 text-success-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Owed to You
                  </dt>
                  <dd className="text-lg font-medium text-success-600">
                    {formatCurrency(overallBalance?.total_owed_to_you || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Amount You Owe */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-6 w-6 text-danger-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    You Owe
                  </dt>
                  <dd className="text-lg font-medium text-danger-600">
                    {formatCurrency(overallBalance?.total_you_owe || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Recent Groups */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Your Groups
              </h3>
              <Link
                to="/groups"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all
              </Link>
            </div>

            {!groups || groups.length === 0 ? (
              <div className="text-center py-6">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No groups yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by creating your first group.
                </p>
                <div className="mt-6">
                  <Link
                    to="/groups?create=true"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Group
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {groups.slice(0, 5).map((group) => (
                  <div key={group.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {group.name || 'Untitled group'}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {group.member_count || 0} members
                      </p>
                    </div>
                    <Link
                      to={`/groups/${group.id}`}
                      className="text-sm text-primary-600 hover:text-primary-500"
                    >
                      View
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Expenses */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Recent Expenses
              </h3>
              <Link
                to="/expenses"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all
              </Link>
            </div>

            {!recentExpenses || recentExpenses.length === 0 ? (
              <div className="text-center py-6">
                <Receipt className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No expenses yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Start tracking expenses in your groups.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentExpenses.map((expense) => (
                  <div key={expense.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {expense.description || 'Untitled expense'}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {formatDate(expense.created_at)} • {expense.payer?.full_name || expense.payer?.username || 'Unknown'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(expense.amount)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Expense Modal */}
      <CreateExpenseModal
        isOpen={showExpenseModal}
        onClose={() => setShowExpenseModal(false)}
        onSubmit={handleCreateExpense}
        isLoading={false}
        allowDirectExpense={true}
      />
    </div>
  );
};
