"""
Pydantic schemas for Settlement-related API requests and responses.
"""

from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional
from decimal import Decimal
from app.schemas.user import UserResponse


class SettlementBase(BaseModel):
    """Base settlement schema with common fields."""
    amount: Decimal = Field(..., gt=0, description="Amount being settled")
    description: Optional[str] = Field(None, max_length=200, description="Optional settlement description")
    payment_method: Optional[str] = Field(None, max_length=50, description="Payment method (Cash, Venmo, PayPal, etc.)")


class SettlementCreate(SettlementBase):
    """Schema for creating a new settlement."""
    other_user_id: int = Field(..., description="ID of the other user in the settlement")
    group_id: Optional[int] = Field(None, description="Optional group ID if settlement is group-specific")
    i_paid: bool = Field(..., description="True if current user paid the other user, False if other user paid current user")


class SettlementResponse(SettlementBase):
    """Schema for settlement data in API responses."""
    id: int
    group_id: Optional[int]
    payer_id: int
    payee_id: int
    settled_at: datetime
    
    # Related data
    payer: Optional[UserResponse] = None
    payee: Optional[UserResponse] = None
    
    class Config:
        from_attributes = True


class BalanceItem(BaseModel):
    """Schema for individual balance between two users."""
    user_id: int
    user: UserResponse
    amount: Decimal  # Positive means they owe you, negative means you owe them
    
    class Config:
        from_attributes = True


class GroupBalance(BaseModel):
    """Schema for balance summary within a group."""
    group_id: int
    group_name: str
    balances: List[BalanceItem]
    total_owed_to_you: Decimal
    total_you_owe: Decimal


class OverallBalance(BaseModel):
    """Schema for overall balance summary across all groups."""
    group_balances: List[GroupBalance]
    total_owed_to_you: Decimal
    total_you_owe: Decimal
    net_balance: Decimal  # Positive means you're owed money overall


class SettlementSuggestion(BaseModel):
    """Schema for suggested settlements to minimize transactions."""
    from_user_id: int
    to_user_id: int
    amount: Decimal
    from_user: UserResponse
    to_user: UserResponse
    
    class Config:
        from_attributes = True


class SettlementListResponse(BaseModel):
    """Schema for paginated settlement list responses."""
    settlements: List[SettlementResponse]
    total: int
    page: int
    per_page: int
