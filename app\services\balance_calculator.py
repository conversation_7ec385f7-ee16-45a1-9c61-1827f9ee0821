"""
Balance calculation service for computing who owes whom and how much.

This service handles the core business logic of calculating balances
between users based on expenses and settlements.
"""

from sqlalchemy.orm import Session
from typing import Dict, List, Tuple
from decimal import Decimal
from collections import defaultdict

from app.models.expense import Expense, ExpenseSplit
from app.models.settlement import Settlement
from app.models.group import GroupMember
from app.models.user import User
from app.models.group_settlement import GroupSettlement


class BalanceCalculator:
    """
    Service class for calculating balances and suggesting settlements.
    
    This class provides methods to:
    1. Calculate balances between users in a group
    2. Calculate overall balances across all groups for a user
    3. Suggest optimal settlements to minimize transactions
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_group_balances(self, group_id: int, user_id: int) -> Dict[int, Decimal]:
        """
        Calculate balances for a user within a specific group.
        
        Args:
            group_id: ID of the group
            user_id: ID of the user to calculate balances for
            
        Returns:
            Dictionary mapping user_id -> balance amount
            Positive amount means they owe you money
            Negative amount means you owe them money
        """
        balances = defaultdict(Decimal)
        
        # Get all expenses in the group
        expenses = self.db.query(Expense).filter(Expense.group_id == group_id).all()
        
        for expense in expenses:
            # If current user paid for the expense
            if expense.payer_id == user_id:
                # Add what others owe to current user
                for split in expense.splits:
                    if split.user_id != user_id:
                        balances[split.user_id] += split.amount_owed
            else:
                # If current user owes money for this expense
                user_split = next((s for s in expense.splits if s.user_id == user_id), None)
                if user_split:
                    balances[expense.payer_id] -= user_split.amount_owed
        
        # Subtract settlements
        # Settlements where current user paid someone
        paid_settlements = self.db.query(Settlement).filter(
            Settlement.payer_id == user_id,
            Settlement.group_id == group_id
        ).all()
        
        for settlement in paid_settlements:
            balances[settlement.payee_id] -= settlement.amount
        
        # Settlements where someone paid current user
        received_settlements = self.db.query(Settlement).filter(
            Settlement.payee_id == user_id,
            Settlement.group_id == group_id
        ).all()
        
        for settlement in received_settlements:
            balances[settlement.payer_id] += settlement.amount
        
        # Remove zero balances and return
        return {user_id: amount for user_id, amount in balances.items() if amount != 0}

    def calculate_all_group_balances(self, group_id: int) -> Dict[int, Decimal]:
        """
        Calculate net balances for ALL users in a specific group.

        This method calculates the net balance for each user in the group,
        showing how much each user owes or is owed overall in the group.

        Args:
            group_id: ID of the group

        Returns:
            Dictionary mapping user_id to their net balance in the group
            Positive = user is owed money, Negative = user owes money
        """
        # Get all group members
        members = self.db.query(GroupMember).filter(GroupMember.group_id == group_id).all()
        member_ids = [m.user_id for m in members]

        # Initialize balances for all members
        net_balances = {user_id: Decimal('0') for user_id in member_ids}

        # Calculate each member's individual balances with others
        for user_id in member_ids:
            user_balances = self.calculate_group_balances(group_id, user_id)

            # Sum up this user's total balance (what they're owed minus what they owe)
            total_balance = sum(user_balances.values())
            net_balances[user_id] = total_balance

        # Filter out zero balances
        return {user_id: balance for user_id, balance in net_balances.items() if abs(balance) >= Decimal('0.01')}

    def calculate_overall_balances(self, user_id: int) -> Dict[int, Decimal]:
        """
        Calculate overall balances for a user across all groups.
        
        Args:
            user_id: ID of the user to calculate balances for
            
        Returns:
            Dictionary mapping user_id -> total balance amount across all groups
        """
        overall_balances = defaultdict(Decimal)
        
        # Get all groups the user is a member of
        group_memberships = self.db.query(GroupMember).filter(
            GroupMember.user_id == user_id
        ).all()
        
        for membership in group_memberships:
            group_balances = self.calculate_group_balances(membership.group_id, user_id)
            
            # Add to overall balances
            for other_user_id, amount in group_balances.items():
                overall_balances[other_user_id] += amount
        
        # Remove zero balances and return
        return {user_id: amount for user_id, amount in overall_balances.items() if amount != 0}
    
    def suggest_settlements(self, group_id: int) -> List[Dict]:
        """
        Suggest optimal settlements to minimize the number of transactions.
        
        This implements a simplified debt settlement algorithm that tries to
        minimize the number of transactions needed to settle all debts.
        
        Args:
            group_id: ID of the group to calculate settlements for
            
        Returns:
            List of settlement suggestions with from_user_id, to_user_id, and amount
        """
        # Calculate net balance for each member using the correct method
        group_balances = self.calculate_all_group_balances(group_id)

        # Filter out very small balances (less than 1 cent) to avoid micro-transactions
        net_balances = {
            user_id: balance for user_id, balance in group_balances.items()
            if abs(balance) >= Decimal('0.01')
        }
        
        # Separate creditors (positive balance) and debtors (negative balance)
        creditors = [(user_id, amount) for user_id, amount in net_balances.items() if amount > Decimal('0')]
        debtors = [(user_id, -amount) for user_id, amount in net_balances.items() if amount < Decimal('0')]
        
        # Sort by amount (largest first)
        creditors.sort(key=lambda x: x[1], reverse=True)
        debtors.sort(key=lambda x: x[1], reverse=True)
        
        settlements = []
        i, j = 0, 0
        
        # Match creditors with debtors
        while i < len(creditors) and j < len(debtors):
            creditor_id, credit_amount = creditors[i]
            debtor_id, debt_amount = debtors[j]
            
            # Settle the minimum of what's owed and what's due
            settle_amount = min(credit_amount, debt_amount)
            
            settlements.append({
                'from_user_id': debtor_id,
                'to_user_id': creditor_id,
                'amount': float(settle_amount)  # Convert Decimal to float for JSON serialization
            })
            
            # Update remaining amounts
            creditors[i] = (creditor_id, credit_amount - settle_amount)
            debtors[j] = (debtor_id, debt_amount - settle_amount)
            
            # Move to next creditor/debtor if current one is settled
            if creditors[i][1] == 0:
                i += 1
            if debtors[j][1] == 0:
                j += 1
        
        return settlements
    
    def get_user_summary(self, user_id: int) -> Dict:
        """
        Get a comprehensive balance summary for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dictionary with total amounts owed to user, owed by user, and net balance
        """
        overall_balances = self.calculate_overall_balances(user_id)
        
        total_owed_to_user = sum(amount for amount in overall_balances.values() if amount > 0)
        total_owed_by_user = sum(-amount for amount in overall_balances.values() if amount < 0)
        net_balance = total_owed_to_user - total_owed_by_user
        
        return {
            'total_owed_to_user': total_owed_to_user,
            'total_owed_by_user': total_owed_by_user,
            'net_balance': net_balance,
            'individual_balances': overall_balances
        }

    def is_group_settled_for_user(self, group_id: int, user_id: int) -> bool:
        """
        Check if a user has already settled all their debts in a group.

        This method checks if the user's current balance is zero, which means
        they don't owe anyone and no one owes them in this group.

        Args:
            group_id: ID of the group to check
            user_id: ID of the user to check

        Returns:
            True if user has zero balance in the group (completely settled)
        """
        # Check if user actually has zero balance in the group
        balances = self.calculate_all_group_balances(group_id)
        user_balance = balances.get(user_id, Decimal('0'))

        # User is considered settled if their balance is zero (or very close to zero)
        return abs(user_balance) < Decimal('0.01')


