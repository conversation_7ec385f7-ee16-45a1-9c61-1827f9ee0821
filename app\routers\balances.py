"""
Balance and settlement router for calculating and displaying user balances.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict
from decimal import Decimal

from app.database import get_db
from app.models.user import User
from app.models.group import Group, GroupMember
from app.schemas.settlement import (
    BalanceItem, GroupBalance, OverallBalance, SettlementSuggestion
)
from app.schemas.user import UserResponse
from app.auth import get_current_active_user
from app.services.balance_calculator import BalanceCalculator

router = APIRouter(prefix="/balances", tags=["Balances"])


@router.get("/groups/{group_id}", response_model=GroupBalance)
def get_group_balance(
    group_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get balance summary for a specific group.
    
    Shows how much each member owes to or is owed by the current user.
    User must be a member of the group.
    """
    # Check if user is a member of the group
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get group information
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Calculate balances
    calculator = BalanceCalculator(db)
    balances = calculator.calculate_group_balances(group_id, current_user.id)
    
    # Convert to response format
    balance_items = []
    total_owed_to_you = Decimal('0')
    total_you_owe = Decimal('0')
    
    for user_id, amount in balances.items():
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            balance_items.append(BalanceItem(
                user_id=user_id,
                user=UserResponse.model_validate(user),
                amount=amount
            ))
            
            if amount > 0:
                total_owed_to_you += amount
            else:
                total_you_owe += -amount
    
    return GroupBalance(
        group_id=group_id,
        group_name=group.name,
        balances=balance_items,
        total_owed_to_you=total_owed_to_you,
        total_you_owe=total_you_owe
    )


@router.get("/overall", response_model=OverallBalance)
def get_overall_balance(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get overall balance summary across all groups.
    
    Shows total amounts owed to and by the current user across all groups.
    """
    calculator = BalanceCalculator(db)
    
    # Get user's group memberships
    memberships = db.query(GroupMember).filter(
        GroupMember.user_id == current_user.id
    ).all()
    
    group_balances = []
    total_owed_to_you = Decimal('0')
    total_you_owe = Decimal('0')
    
    for membership in memberships:
        group = db.query(Group).filter(Group.id == membership.group_id).first()
        if not group:
            continue
            
        # Calculate balances for this group
        balances = calculator.calculate_group_balances(membership.group_id, current_user.id)
        
        # Convert to balance items
        balance_items = []
        group_owed_to_you = Decimal('0')
        group_you_owe = Decimal('0')
        
        for user_id, amount in balances.items():
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                balance_items.append(BalanceItem(
                    user_id=user_id,
                    user=UserResponse.model_validate(user),
                    amount=amount
                ))
                
                if amount > 0:
                    group_owed_to_you += amount
                    total_owed_to_you += amount
                else:
                    group_you_owe += -amount
                    total_you_owe += -amount
        
        if balance_items:  # Only include groups with non-zero balances
            group_balances.append(GroupBalance(
                group_id=membership.group_id,
                group_name=group.name,
                balances=balance_items,
                total_owed_to_you=group_owed_to_you,
                total_you_owe=group_you_owe
            ))
    
    net_balance = total_owed_to_you - total_you_owe
    
    return OverallBalance(
        group_balances=group_balances,
        total_owed_to_you=total_owed_to_you,
        total_you_owe=total_you_owe,
        net_balance=net_balance
    )


@router.get("/groups/{group_id}/suggestions", response_model=List[SettlementSuggestion])
def get_settlement_suggestions(
    group_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get suggested settlements to minimize transactions in a group.
    
    Returns optimal settlement suggestions that minimize the number of
    transactions needed to settle all debts in the group.
    """
    # Check if user is a member of the group
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Calculate settlement suggestions first
    calculator = BalanceCalculator(db)
    suggestions = calculator.suggest_settlements(group_id)

    # Filter suggestions to only show ones involving the current user
    # This prevents showing settlements between other users
    user_suggestions = [
        suggestion for suggestion in suggestions
        if suggestion['from_user_id'] == current_user.id or suggestion['to_user_id'] == current_user.id
    ]

    # If no suggestions for this user, they're settled up
    if not user_suggestions:
        return []

    # Convert to response format
    settlement_suggestions = []
    for suggestion in user_suggestions:
        from_user = db.query(User).filter(User.id == suggestion['from_user_id']).first()
        to_user = db.query(User).filter(User.id == suggestion['to_user_id']).first()

        if from_user and to_user:
            settlement_suggestions.append(SettlementSuggestion(
                from_user_id=suggestion['from_user_id'],
                to_user_id=suggestion['to_user_id'],
                amount=suggestion['amount'],
                from_user=UserResponse.model_validate(from_user),
                to_user=UserResponse.model_validate(to_user)
            ))

    return settlement_suggestions
