/**
 * Settle Up Modal Component
 * Enhanced settlement interface similar to Splitwise
 */

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { X, DollarSign, Users, CheckCircle, CreditCard, Banknote } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { balanceService } from '../../services/balanceService';

interface SettleUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    from_user_id: number;
    to_user_id: number;
    from_user: {
      id: number;
      username: string;
      full_name?: string;
    };
    to_user: {
      id: number;
      username: string;
      full_name?: string;
    };
    amount: number;
  };
  groupId?: number;
}

export const SettleUpModal: React.FC<SettleUpModalProps> = ({
  isOpen,
  onClose,
  suggestion,
  groupId,
}) => {
  const [settleAmount, setSettleAmount] = useState(suggestion.amount);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'venmo' | 'paypal' | 'bank' | 'other'>('cash');
  const [notes, setNotes] = useState('');
  const queryClient = useQueryClient();

  const createSettlementMutation = useMutation({
    mutationFn: balanceService.createSettlement,
    onSuccess: () => {
      // Invalidate all balance-related queries to force refresh
      queryClient.invalidateQueries({ queryKey: ['overall-balance'] });
      queryClient.invalidateQueries({ queryKey: ['settlement-suggestions'] });
      queryClient.invalidateQueries({ queryKey: ['group-balances'] });
      queryClient.invalidateQueries({ queryKey: ['group-balance'] });
      queryClient.invalidateQueries({ queryKey: ['all-expenses'] });

      // Force refetch of current data
      queryClient.refetchQueries({ queryKey: ['overall-balance'] });
      if (groupId) {
        queryClient.refetchQueries({ queryKey: ['settlement-suggestions', groupId] });
        queryClient.refetchQueries({ queryKey: ['group-balance', groupId] });
      }

      toast.success('Settlement recorded successfully! 🎉');
      onClose();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const handleSettle = () => {
    if (settleAmount <= 0) {
      toast.error('Settlement amount must be greater than 0');
      return;
    }

    if (settleAmount > suggestion.amount) {
      toast.error('Settlement amount cannot exceed the owed amount');
      return;
    }

    const description = notes || 
      `${paymentMethod.charAt(0).toUpperCase() + paymentMethod.slice(1)} payment from ${
        suggestion.from_user.full_name || suggestion.from_user.username
      } to ${suggestion.to_user.full_name || suggestion.to_user.username}`;

    createSettlementMutation.mutate({
      payee_id: suggestion.to_user_id,
      amount: settleAmount,
      group_id: groupId,
      description,
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const paymentMethods = [
    { id: 'cash', label: 'Cash', icon: Banknote },
    { id: 'venmo', label: 'Venmo', icon: CreditCard },
    { id: 'paypal', label: 'PayPal', icon: CreditCard },
    { id: 'bank', label: 'Bank Transfer', icon: CreditCard },
    { id: 'other', label: 'Other', icon: DollarSign },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Settle Up</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Settlement Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Users className="h-5 w-5 text-gray-500" />
              <span className="font-medium text-gray-900">Settlement Details</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>
                <span className="font-medium">
                  {suggestion.from_user.full_name || suggestion.from_user.username}
                </span>
                {' '}owes{' '}
                <span className="font-medium">
                  {suggestion.to_user.full_name || suggestion.to_user.username}
                </span>
              </p>
              <p className="text-lg font-bold text-primary-600 mt-1">
                {formatCurrency(suggestion.amount)}
              </p>
            </div>
          </div>

          {/* Settlement Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Settlement Amount
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="number"
                value={settleAmount}
                onChange={(e) => setSettleAmount(parseFloat(e.target.value) || 0)}
                max={suggestion.amount}
                min={0}
                step={0.01}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Maximum: {formatCurrency(suggestion.amount)}
            </p>
          </div>

          {/* Payment Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Payment Method
            </label>
            <div className="grid grid-cols-2 gap-2">
              {paymentMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <button
                    key={method.id}
                    onClick={() => setPaymentMethod(method.id as any)}
                    className={`flex items-center space-x-2 p-3 rounded-lg border-2 transition-colors ${
                      paymentMethod === method.id
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{method.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Add a note about this payment..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </button>
          <button
            onClick={handleSettle}
            disabled={createSettlementMutation.isPending || settleAmount <= 0}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {createSettlementMutation.isPending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Record Settlement
          </button>
        </div>
      </div>
    </div>
  );
};
