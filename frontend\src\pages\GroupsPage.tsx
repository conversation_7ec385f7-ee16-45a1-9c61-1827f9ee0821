/**
 * Groups Page Component
 * Displays all user groups with ability to create new ones
 */

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useSearchParams } from 'react-router-dom';
import { Plus, Users, Calendar, Settings, Trash2, AlertTriangle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { groupService } from '../services/groupService';
import { CreateGroupModal } from '../components/groups/CreateGroupModal';
import { useAuthStore } from '../stores/authStore';

export const GroupsPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);
  const { user } = useAuthStore();
  const queryClient = useQueryClient();

  // Check if we should auto-open create modal
  useEffect(() => {
    if (searchParams.get('create') === 'true') {
      setIsCreateModalOpen(true);
      // Remove the parameter from URL
      searchParams.delete('create');
      setSearchParams(searchParams);
    }
  }, [searchParams, setSearchParams]);

  // Fetch user's groups
  const { data: groups = [], isLoading } = useQuery({
    queryKey: ['groups'],
    queryFn: groupService.getMyGroups,
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: groupService.createGroup,
    onSuccess: (newGroup) => {
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      toast.success(`Group "${newGroup.name}" created successfully!`);
      setIsCreateModalOpen(false);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: groupService.deleteGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      toast.success('Group deleted successfully!');
      setShowDeleteConfirm(null);
    },
    onError: (error: Error) => {
      toast.error(error.message);
      setShowDeleteConfirm(null);
    },
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Groups</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your expense groups and create new ones.
          </p>
        </div>
        <div className="w-full sm:w-auto">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="btn-primary w-full sm:w-auto justify-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Group
          </button>
        </div>
      </div>

      {/* Groups Grid */}
      {groups.length === 0 ? (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No groups yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first expense group.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Group
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {groups.map((group) => (
            <div key={group.id} className="card hover:shadow-lg transition-shadow">
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {group.name}
                    </h3>
                    {group.description && (
                      <p className="text-sm text-gray-600 mb-3">
                        {group.description}
                      </p>
                    )}
                    
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <Users className="h-4 w-4 mr-1" />
                      {group.member_count || 0} members
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500 mb-4">
                      <Calendar className="h-4 w-4 mr-1" />
                      Created {formatDate(group.created_at)}
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0 ml-4 flex space-x-2">
                    <Link
                      to={`/groups/${group.id}`}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Settings className="h-5 w-5" />
                    </Link>
                    {user && group.creator_id === user.id && (
                      <button
                        onClick={() => setShowDeleteConfirm(group.id)}
                        className="text-red-400 hover:text-red-600"
                        title="Delete Group"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="flex space-x-3">
                  <Link
                    to={`/groups/${group.id}`}
                    className="flex-1 btn-primary text-center"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Group Modal */}
      <CreateGroupModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={(data) => createGroupMutation.mutate(data)}
        isLoading={createGroupMutation.isPending}
      />

      {/* Delete Group Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">Delete Group</h3>
              </div>
              <p className="text-sm text-gray-500 mb-6">
                Are you sure you want to delete this group? This will permanently delete all expenses,
                splits, settlements, and memberships associated with this group.
              </p>
              <p className="text-sm text-red-600 font-medium mb-6">
                This action cannot be undone.
              </p>
              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => deleteGroupMutation.mutate(showDeleteConfirm)}
                  disabled={deleteGroupMutation.isPending}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  {deleteGroupMutation.isPending ? 'Deleting...' : 'Delete Group'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
