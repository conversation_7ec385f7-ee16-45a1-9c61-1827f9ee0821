"""
Expense management router for creating and managing shared expenses.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List
from decimal import Decimal

from app.database import get_db
from app.models.user import User
from app.models.group import GroupMember
from app.models.expense import Expense, ExpenseSplit
from app.schemas.expense import (
    ExpenseCreate, ExpenseUpdate, ExpenseResponse, ExpenseListResponse
)
from app.auth import get_current_active_user

router = APIRouter(prefix="/expenses", tags=["Expenses"])


def validate_group_membership(db: Session, group_id: int, user_id: int) -> bool:
    """Check if a user is a member of a group."""
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == user_id
    ).first()
    return membership is not None


def create_expense_splits(
    db: Session,
    expense: Expense,
    split_type: str,
    participants: List[int],
    custom_splits: List = None
) -> None:
    """
    Create expense splits based on split type.

    Args:
        db: Database session
        expense: The expense object
        split_type: 'equal', 'custom', or 'shares'
        participants: List of user IDs to split with
        custom_splits: List of custom split amounts/shares
    """
    if split_type == "equal":
        # Calculate equal split amount
        amount_per_person = expense.amount / len(participants)

        # Create splits for each participant
        for user_id in participants:
            split = ExpenseSplit(
                expense_id=expense.id,
                user_id=user_id,
                amount_owed=amount_per_person
            )
            db.add(split)

    elif split_type == "custom":
        # Validate that custom splits sum to total amount
        total_custom = sum(split.amount_owed for split in custom_splits)
        if abs(total_custom - expense.amount) > Decimal('0.01'):  # Allow for small rounding differences
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Custom splits total ({total_custom}) must equal expense amount ({expense.amount})"
            )

        # Create splits from custom amounts
        for split_data in custom_splits:
            split = ExpenseSplit(
                expense_id=expense.id,
                user_id=split_data.user_id,
                amount_owed=split_data.amount_owed
            )
            db.add(split)

    elif split_type == "shares":
        # Calculate total shares
        total_shares = sum(split.shares for split in custom_splits)
        if total_shares <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Total shares must be greater than 0"
            )

        # Create splits based on shares proportion
        for split_data in custom_splits:
            # Calculate amount based on share proportion
            share_proportion = Decimal(split_data.shares) / Decimal(total_shares)
            amount_owed = expense.amount * share_proportion

            split = ExpenseSplit(
                expense_id=expense.id,
                user_id=split_data.user_id,
                amount_owed=amount_owed,
                shares=split_data.shares
            )
            db.add(split)


@router.post("/", response_model=ExpenseResponse, status_code=status.HTTP_201_CREATED)
def create_expense(
    expense_data: ExpenseCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new expense in a group or as a direct expense with friends.

    The expense can be split equally among participants or with custom amounts.
    The current user is set as the payer if not specified.

    If group_id is provided, this creates a group expense.
    If group_id is None, this creates a direct expense with specified participants.
    """
    # Set payer to current user if not specified
    payer_id = expense_data.payer_id or current_user.id

    if expense_data.group_id:
        # GROUP EXPENSE: Validate group membership
        if not validate_group_membership(db, expense_data.group_id, current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this group"
            )

        # Validate that payer is a member of the group
        if not validate_group_membership(db, expense_data.group_id, payer_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payer must be a member of the group"
            )

        # Validate that all participants are members of the group
        for participant_id in expense_data.participants:
            if not validate_group_membership(db, expense_data.group_id, participant_id):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User {participant_id} is not a member of this group"
                )
    else:
        # DIRECT EXPENSE: Validate participants exist and current user is involved
        if current_user.id not in expense_data.participants:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You must be included as a participant in direct expenses"
            )

        # Validate that all participants exist
        for participant_id in expense_data.participants:
            participant = db.query(User).filter(User.id == participant_id).first()
            if not participant:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User {participant_id} not found"
                )
            if not participant.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User {participant.username} is not active"
                )

        # Validate that payer is one of the participants
        if payer_id not in expense_data.participants:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payer must be one of the participants in direct expenses"
            )
    
    # Create the expense
    db_expense = Expense(
        group_id=expense_data.group_id,
        description=expense_data.description,
        amount=expense_data.amount,
        payer_id=payer_id,
        category=expense_data.category,
        notes=expense_data.notes
    )
    
    db.add(db_expense)
    db.commit()
    db.refresh(db_expense)
    
    # Create expense splits
    create_expense_splits(
        db, 
        db_expense, 
        expense_data.split_type, 
        expense_data.participants,
        expense_data.custom_splits
    )
    
    db.commit()
    db.refresh(db_expense)
    
    return db_expense


@router.get("/", response_model=List[ExpenseResponse])
def get_expenses(
    group_id: int = Query(..., description="Group ID to get expenses for"),
    skip: int = Query(0, ge=0, description="Number of expenses to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of expenses to return"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get expenses for a specific group.
    
    Returns expenses with payer information and split details.
    User must be a member of the group.
    """
    # Validate that user is a member of the group
    if not validate_group_membership(db, group_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get expenses for the group
    expenses = db.query(Expense).filter(
        Expense.group_id == group_id
    ).order_by(
        Expense.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    return expenses


@router.get("/{expense_id}", response_model=ExpenseResponse)
def get_expense(
    expense_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific expense.
    
    Includes expense details, payer info, and all splits.
    User must be a member of the group containing the expense.
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Validate that user is a member of the group
    if not validate_group_membership(db, expense.group_id, current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    return expense


@router.put("/{expense_id}", response_model=ExpenseResponse)
def update_expense(
    expense_id: int,
    expense_update: ExpenseUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update an existing expense.
    
    Only the expense creator (payer) can update the expense.
    Note: Updating splits requires deleting and recreating the expense.
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Check if user is the payer (creator) of the expense
    if expense.payer_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the expense payer can update the expense"
        )
    
    # Update fields if provided
    if expense_update.description is not None:
        expense.description = expense_update.description
    if expense_update.amount is not None:
        expense.amount = expense_update.amount
    if expense_update.category is not None:
        expense.category = expense_update.category
    if expense_update.notes is not None:
        expense.notes = expense_update.notes
    
    db.commit()
    db.refresh(expense)
    
    return expense


@router.delete("/{expense_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_expense(
    expense_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete an expense.
    
    Only the expense creator (payer) can delete the expense.
    This will also delete all associated splits.
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Check if user is the payer (creator) of the expense
    if expense.payer_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the expense payer can delete the expense"
        )
    
    # Delete the expense (splits will be deleted due to cascade)
    db.delete(expense)
    db.commit()
