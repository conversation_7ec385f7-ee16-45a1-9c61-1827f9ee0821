/**
 * Expenses Page Component
 * Shows all expenses across all groups with filtering options
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Receipt, Calendar, User, Filter } from 'lucide-react';
import { groupService } from '../services/groupService';
import { expenseService } from '../services/expenseService';
import { Expense } from '../types/api';

export const ExpensesPage: React.FC = () => {
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);

  // Fetch user's groups
  const { data: groups = [] } = useQuery({
    queryKey: ['groups'],
    queryFn: groupService.getMyGroups,
  });

  // Fetch expenses for all groups
  const { data: allExpenses = [], isLoading } = useQuery({
    queryKey: ['all-expenses', selectedGroupId],
    queryFn: async () => {
      if (selectedGroupId) {
        return expenseService.getExpenses(selectedGroupId);
      }
      
      // Fetch expenses from all groups
      const expensePromises = groups.map(group => 
        expenseService.getExpenses(group.id)
      );
      
      const allGroupExpenses = await Promise.all(expensePromises);
      return allGroupExpenses.flat().sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    },
    enabled: groups.length > 0,
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getGroupName = (groupId: number) => {
    const group = groups.find(g => g.id === groupId);
    return group?.name || 'Unknown Group';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">All Expenses</h1>
        <p className="mt-1 text-sm text-gray-600">
          View and manage expenses across all your groups.
        </p>
      </div>

      {/* Filters */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <Filter className="h-4 w-4 mr-2 text-gray-400" />
            <label htmlFor="group-filter" className="text-sm font-medium text-gray-700">
              Filter by group:
            </label>
          </div>
          <select
            id="group-filter"
            value={selectedGroupId || ''}
            onChange={(e) => setSelectedGroupId(e.target.value ? parseInt(e.target.value) : null)}
            className="input-field w-auto"
          >
            <option value="">All Groups</option>
            {groups.map((group) => (
              <option key={group.id} value={group.id}>
                {group.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Expenses List */}
      {allExpenses.length === 0 ? (
        <div className="text-center py-12">
          <Receipt className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No expenses found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedGroupId
              ? 'No expenses in the selected group yet.'
              : 'Start by adding expenses to your groups.'
            }
          </p>
        </div>
      ) : (
        <>
          {/* Desktop Table View */}
          <div className="hidden md:block card">
            <div className="card-body p-0">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Group
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Paid By
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Participants
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {allExpenses.map((expense) => (
                      <tr key={expense.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {expense.description}
                            </div>
                            {expense.category && (
                              <div className="text-sm text-gray-500">
                                {expense.category}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                            {getGroupName(expense.group_id)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-400" />
                            <div className="text-sm text-gray-900">
                              {expense.payer?.full_name || expense.payer?.username}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatCurrency(expense.amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-500">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(expense.created_at)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {expense.splits?.length || 0} people
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden space-y-4">
            {allExpenses.map((expense) => (
              <div key={expense.id} className="card">
                <div className="card-body">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {expense.description}
                      </h3>
                      {expense.category && (
                        <p className="text-xs text-gray-500 mt-1">
                          {expense.category}
                        </p>
                      )}
                    </div>
                    <div className="text-lg font-semibold text-gray-900 ml-3">
                      {formatCurrency(expense.amount)}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Group:</span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {getGroupName(expense.group_id)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Paid by:</span>
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1 text-gray-400" />
                        <span className="text-xs text-gray-900">
                          {expense.payer?.full_name || expense.payer?.username}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Date:</span>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {formatDate(expense.created_at)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Participants:</span>
                      <span className="text-xs text-gray-500">
                        {expense.splits?.length || 0} people
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
