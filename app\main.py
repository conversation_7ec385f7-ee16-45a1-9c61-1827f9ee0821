"""
Main FastAPI application for the Bhagbanda backend.

This file sets up the FastAPI app, includes all routers, and configures
middleware and startup events.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from contextlib import asynccontextmanager
import os

from app.config import settings
from app.database import create_tables
from app.database_backup import initialize_persistent_database, backup_database_on_shutdown
from app.routers import auth, users, groups, expenses, balances, settlements, admin, admin_groups, admin_expenses


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    This function runs during app startup and shutdown.
    We use it to create database tables on startup.
    """
    # Startup
    print("🚀 Starting Bhagbanda application...")

    # Initialize persistent database
    initialize_persistent_database()

    # Create database tables
    create_tables()

    print("✅ Application startup complete!")

    yield

    # Shutdown
    print("🛑 Shutting down Bhagbanda application...")
    backup_database_on_shutdown()
    print("✅ Application shutdown complete!")


# Create FastAPI application
app = FastAPI(
    title=settings.project_name,
    version=settings.version,
    description=settings.description,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix=settings.api_v1_prefix)
app.include_router(users.router, prefix=settings.api_v1_prefix)
app.include_router(groups.router, prefix=settings.api_v1_prefix)
app.include_router(expenses.router, prefix=settings.api_v1_prefix)
app.include_router(balances.router, prefix=settings.api_v1_prefix)
app.include_router(settlements.router, prefix=settings.api_v1_prefix)
app.include_router(admin.router, prefix=settings.api_v1_prefix)
app.include_router(admin_groups.router, prefix=settings.api_v1_prefix)
app.include_router(admin_expenses.router, prefix=settings.api_v1_prefix)

# Serve static files (React frontend)
static_dir = "static"
if os.path.exists(static_dir):
    # Mount the entire static directory at root to serve assets directly
    app.mount("/assets", StaticFiles(directory=os.path.join(static_dir, "assets")), name="assets")
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


# Root endpoint moved to catch-all route to serve React app


@app.get("/health")
def health_check():
    """
    Health check endpoint for monitoring and load balancers.
    """
    return {"status": "healthy", "version": settings.version}


# API info endpoint (for /api route)
@app.get("/api")
def api_info():
    """API information endpoint."""
    return {
        "message": "Welcome to Bhagbanda Backend API",
        "version": settings.version,
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }

# Serve React app for all non-API routes (must be last)
@app.get("/{full_path:path}")
async def serve_react_app(full_path: str):
    """Serve React app for all non-API routes."""
    # Don't serve React app for API routes, docs, static files, or assets
    if (full_path.startswith("api") or
        full_path.startswith("docs") or
        full_path.startswith("redoc") or
        full_path.startswith("static") or
        full_path.startswith("assets") or  # Add this line
        full_path.endswith(".js") or       # Add this line
        full_path.endswith(".css") or      # Add this line
        full_path.endswith(".svg") or      # Add this line
        full_path.endswith(".ico") or      # Add this line
        full_path == "health"):
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Not found")

    # Serve index.html for all routes (including root) - React Router will handle routing
    index_file = os.path.join(static_dir, "index.html")
    if os.path.exists(index_file):
        return FileResponse(index_file)
    else:
        return {"message": "Frontend not available", "note": "Build and copy frontend to static/ directory"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
