"""
Expense and ExpenseSplit models for managing shared expenses.
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Numeric, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class Expense(Base):
    """
    Expense model representing a shared expense within a group.
    
    Attributes:
        id: Primary key
        group_id: ID of the group this expense belongs to
        description: What the expense was for (e.g., "Dinner at restaurant")
        amount: Total amount of the expense (stored as decimal for precision)
        payer_id: ID of the user who paid for the expense
        created_at: When the expense was recorded
        updated_at: When the expense was last updated
    """
    __tablename__ = "expenses"
    
    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey("groups.id"), nullable=True)  # Optional for direct expenses
    description = Column(String(200), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)  # Precision for currency
    payer_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Optional: Category for expense (food, transport, accommodation, etc.)
    category = Column(String(50), nullable=True)
    
    # Optional: Additional notes
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    group = relationship("Group", back_populates="expenses")
    payer = relationship("User", back_populates="paid_expenses")
    splits = relationship("ExpenseSplit", back_populates="expense", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Expense(id={self.id}, description='{self.description}', amount={self.amount})>"


class ExpenseSplit(Base):
    """
    ExpenseSplit model representing how an expense is split among users.

    This allows for equal splits, custom splits, and shares-based splits.
    For equal splits, we calculate the amount per person.
    For custom splits, each user can owe a different amount.
    For shares splits, each user gets a number of shares and amount is calculated proportionally.

    Attributes:
        id: Primary key
        expense_id: ID of the expense being split
        user_id: ID of the user who owes this portion
        amount_owed: How much this user owes for this expense
        shares: Number of shares for this user (for shares-based splits)
    """
    __tablename__ = "expense_splits"

    id = Column(Integer, primary_key=True, index=True)
    expense_id = Column(Integer, ForeignKey("expenses.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount_owed = Column(Numeric(10, 2), nullable=False)
    shares = Column(Integer, nullable=True)  # For shares-based splits
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    expense = relationship("Expense", back_populates="splits")
    user = relationship("User", back_populates="expense_splits")
    
    def __repr__(self):
        return f"<ExpenseSplit(expense_id={self.expense_id}, user_id={self.user_id}, amount_owed={self.amount_owed})>"
