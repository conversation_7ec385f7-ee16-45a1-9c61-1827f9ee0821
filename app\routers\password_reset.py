"""
Password reset router for handling forgot password functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from datetime import datetime, timedelta
import secrets
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from app.database import get_db
from app.models.user import User
from app.auth import get_password_hash
from app.config import settings
from app.services.email_service import email_service

router = APIRouter(prefix="/password-reset", tags=["Password Reset"])


class ForgotPasswordRequest(BaseModel):
    """Request schema for forgot password."""
    email: EmailStr


class ResetPasswordRequest(BaseModel):
    """Request schema for resetting password."""
    token: str
    new_password: str


class PasswordResetToken(BaseModel):
    """Password reset token model."""
    id: int
    user_id: int
    token: str
    expires_at: datetime
    used: bool = False


# In-memory storage for reset tokens (in production, use Redis or database)
reset_tokens = {}


def send_password_reset_email(email: str, reset_token: str, background_tasks: BackgroundTasks):
    """Send password reset email to user."""
    background_tasks.add_task(
        email_service.send_password_reset_email,
        user_email=email,
        reset_token=reset_token
    )


@router.post("/forgot-password")
def forgot_password(
    request: ForgotPasswordRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Send password reset email to user.
    
    This endpoint accepts an email address and sends a password reset link
    if the email is associated with a user account.
    """
    # Find user by email
    user = db.query(User).filter(User.email == request.email).first()
    
    if not user:
        # Don't reveal whether email exists or not for security
        return {"message": "If an account with that email exists, a password reset link has been sent."}
    
    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour
    
    # Store token (in production, store in database or Redis)
    reset_tokens[reset_token] = {
        "user_id": user.id,
        "email": user.email,
        "expires_at": expires_at,
        "used": False
    }
    
    # Send email
    send_password_reset_email(user.email, reset_token, background_tasks)
    
    return {"message": "If an account with that email exists, a password reset link has been sent."}


@router.post("/reset-password")
def reset_password(
    request: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """
    Reset user password using reset token.
    
    This endpoint accepts a reset token and new password to update
    the user's password.
    """
    # Validate token
    token_data = reset_tokens.get(request.token)
    
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Check if token is expired
    if datetime.utcnow() > token_data["expires_at"]:
        # Remove expired token
        del reset_tokens[request.token]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has expired"
        )
    
    # Check if token has been used
    if token_data["used"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has already been used"
        )
    
    # Find user
    user = db.query(User).filter(User.id == token_data["user_id"]).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update password
    user.password_hash = get_password_hash(request.new_password)
    
    # Mark token as used
    token_data["used"] = True
    
    db.commit()
    
    return {"message": "Password has been reset successfully"}


@router.get("/validate-token/{token}")
def validate_reset_token(token: str):
    """
    Validate if a reset token is valid and not expired.
    
    This endpoint is used by the frontend to check if a reset token
    is valid before showing the reset password form.
    """
    token_data = reset_tokens.get(token)
    
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid reset token"
        )
    
    # Check if token is expired
    if datetime.utcnow() > token_data["expires_at"]:
        # Remove expired token
        del reset_tokens[token]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has expired"
        )
    
    # Check if token has been used
    if token_data["used"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset token has already been used"
        )
    
    return {
        "valid": True,
        "email": token_data["email"],
        "expires_at": token_data["expires_at"]
    }
