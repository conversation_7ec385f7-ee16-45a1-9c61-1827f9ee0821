"""
Admin Expenses router for super admin management of all expenses.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from decimal import Decimal

from app.database import get_db
from app.models.user import User
from app.models.group import Group, GroupMember
from app.models.expense import Expense, ExpenseSplit
from app.schemas.expense import ExpenseResponse, ExpenseCreate, ExpenseUpdate
from app.auth import get_current_active_user

router = APIRouter(prefix="/admin/expenses", tags=["Admin Expenses"])


def require_admin(current_user: User = Depends(get_current_active_user)):
    """Require admin privileges for all admin endpoints."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user


@router.get("/", response_model=List[ExpenseResponse])
def get_all_expenses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    group_id: Optional[int] = Query(None),
    search: Optional[str] = Query(None),
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get all expenses in the system (admin only).
    
    Allows admins to view and manage all expenses regardless of group membership.
    """
    query = db.query(Expense)
    
    if group_id:
        query = query.filter(Expense.group_id == group_id)
    
    if search:
        query = query.filter(Expense.description.ilike(f"%{search}%"))
    
    expenses = query.order_by(Expense.created_at.desc()).offset(skip).limit(limit).all()
    
    return [ExpenseResponse.model_validate(expense) for expense in expenses]


@router.get("/{expense_id}", response_model=ExpenseResponse)
def get_expense_details(
    expense_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about any expense (admin only).
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    return ExpenseResponse.model_validate(expense)


@router.post("/", response_model=ExpenseResponse, status_code=status.HTTP_201_CREATED)
def create_expense_as_admin(
    expense_data: ExpenseCreate,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create an expense in any group as any user (admin only).
    
    This allows admins to create expenses on behalf of users.
    """
    # Validate group exists
    group = db.query(Group).filter(Group.id == expense_data.group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Validate payer exists and is a member of the group
    payer = db.query(User).filter(User.id == expense_data.payer_id).first()
    if not payer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payer user not found"
        )
    
    payer_membership = db.query(GroupMember).filter(
        GroupMember.group_id == expense_data.group_id,
        GroupMember.user_id == expense_data.payer_id
    ).first()
    
    if not payer_membership:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payer must be a member of the group"
        )
    
    # Create the expense
    expense = Expense(
        group_id=expense_data.group_id,
        payer_id=expense_data.payer_id,
        amount=expense_data.amount,
        description=expense_data.description,
        category=expense_data.category
    )
    
    db.add(expense)
    db.flush()  # Get the expense ID
    
    # Create expense splits
    from app.routers.expenses import create_expense_splits
    create_expense_splits(
        db=db,
        expense=expense,
        split_type=expense_data.split_type,
        participants=expense_data.participants,
        custom_splits=expense_data.custom_splits
    )
    
    db.commit()
    db.refresh(expense)
    
    return ExpenseResponse.model_validate(expense)


@router.put("/{expense_id}", response_model=ExpenseResponse)
def update_expense_as_admin(
    expense_id: int,
    expense_update: ExpenseUpdate,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update any expense (admin only).
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Update expense fields
    if expense_update.amount is not None:
        expense.amount = expense_update.amount
    if expense_update.description is not None:
        expense.description = expense_update.description
    if expense_update.category is not None:
        expense.category = expense_update.category
    
    # If payer is being changed, validate the new payer
    if expense_update.payer_id is not None:
        new_payer = db.query(User).filter(User.id == expense_update.payer_id).first()
        if not new_payer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="New payer user not found"
            )
        
        # Check if new payer is a member of the group
        payer_membership = db.query(GroupMember).filter(
            GroupMember.group_id == expense.group_id,
            GroupMember.user_id == expense_update.payer_id
        ).first()
        
        if not payer_membership:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="New payer must be a member of the group"
            )
        
        expense.payer_id = expense_update.payer_id
    
    db.commit()
    db.refresh(expense)
    
    return ExpenseResponse.model_validate(expense)


@router.delete("/{expense_id}")
def delete_expense_as_admin(
    expense_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete any expense and all its splits (admin only).
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Delete all expense splits first
    db.query(ExpenseSplit).filter(ExpenseSplit.expense_id == expense_id).delete()
    
    # Delete the expense
    db.delete(expense)
    db.commit()
    
    return {"message": f"Expense '{expense.description}' has been deleted"}


@router.get("/{expense_id}/splits")
def get_expense_splits(
    expense_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get all splits for an expense (admin only).
    """
    expense = db.query(Expense).filter(Expense.id == expense_id).first()
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    splits = db.query(ExpenseSplit).filter(ExpenseSplit.expense_id == expense_id).all()
    
    split_data = []
    for split in splits:
        user = db.query(User).filter(User.id == split.user_id).first()
        split_data.append({
            "id": split.id,
            "user_id": split.user_id,
            "user_name": user.username if user else "Unknown",
            "amount_owed": float(split.amount_owed),
            "shares": split.shares
        })
    
    return {
        "expense_id": expense_id,
        "expense_description": expense.description,
        "total_amount": float(expense.amount),
        "splits": split_data
    }


@router.put("/{expense_id}/splits/{split_id}")
def update_expense_split(
    expense_id: int,
    split_id: int,
    amount_owed: Decimal,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update an expense split amount (admin only).
    """
    split = db.query(ExpenseSplit).filter(
        ExpenseSplit.id == split_id,
        ExpenseSplit.expense_id == expense_id
    ).first()
    
    if not split:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense split not found"
        )
    
    split.amount_owed = amount_owed
    db.commit()
    
    return {"message": "Expense split updated successfully"}


@router.delete("/{expense_id}/splits/{split_id}")
def delete_expense_split(
    expense_id: int,
    split_id: int,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete an expense split (admin only).
    """
    split = db.query(ExpenseSplit).filter(
        ExpenseSplit.id == split_id,
        ExpenseSplit.expense_id == expense_id
    ).first()
    
    if not split:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense split not found"
        )
    
    db.delete(split)
    db.commit()
    
    return {"message": "Expense split deleted successfully"}
