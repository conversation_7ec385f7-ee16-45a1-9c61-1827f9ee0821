#!/usr/bin/env python3
"""
<PERSON>ript to promote the admin user to admin status by directly updating the database.
This script should be run inside the Cloud Run container.
"""

import sqlite3
import os

def promote_user_to_admin():
    """Promote the admin user to admin status."""
    
    # Database path in the container
    db_path = "/app/data/bhagbanda.db"
    
    # Alternative paths to try
    db_paths = [
        "/app/data/bhagbanda.db",
        "/app/bhagbanda.db",
        "./bhagbanda.db",
        "bhagbanda.db"
    ]
    
    db_file = None
    for path in db_paths:
        if os.path.exists(path):
            db_file = path
            break
    
    if not db_file:
        print("❌ Database file not found!")
        print("Checked paths:")
        for path in db_paths:
            print(f"  - {path}")
        return False
    
    try:
        print(f"📁 Using database: {db_file}")
        
        # Connect to database
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Check if admin user exists
        cursor.execute(
            "SELECT id, username, email, full_name, is_admin FROM users WHERE username = ?",
            ("admin",)
        )
        user = cursor.fetchone()
        
        if not user:
            print("❌ Admin user not found!")
            return False
        
        user_id, username, email, full_name, is_admin = user
        
        if is_admin:
            print(f"ℹ️  User {username} is already an admin")
            return True
        
        # Make user admin
        cursor.execute("UPDATE users SET is_admin = 1 WHERE username = ?", ("admin",))
        conn.commit()
        
        print(f"✅ Successfully granted admin privileges to:")
        print(f"   Name: {full_name}")
        print(f"   Username: {username}")
        print(f"   Email: {email}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 Promoting admin user to admin status...")
    success = promote_user_to_admin()
    
    if success:
        print("✅ Admin promotion completed!")
        print("🎉 You can now login with:")
        print("   Username: admin")
        print("   Password: admin6061")
        print("   URL: https://bhagbanda-clean-874960365796.us-central1.run.app")
    else:
        print("❌ Admin promotion failed!")
