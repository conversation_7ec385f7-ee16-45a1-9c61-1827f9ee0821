#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to make a user admin via API call to the running Cloud Run service.
"""

import requests
import json
import sys

def make_admin_via_api(service_url, username):
    """Make a user admin by directly calling the service API."""
    
    try:
        # First, let's try to login as the user to get a token
        login_data = {
            "username": username,
            "password": "admin6061"
        }

        login_response = requests.post(
            f"{service_url}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print(f"✅ Successfully logged in as {username}")
            
            # Now we need to manually update the database
            # Since we can't directly access admin endpoints without being admin,
            # we'll need to use a different approach
            
            print("ℹ️  User created successfully. Admin privileges need to be granted manually.")
            print("ℹ️  Use Google Cloud Console to access the container and run:")
            print(f"   python make_admin.py {username}")
            
            return True
        else:
            print(f"❌ Login failed: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    service_url = "https://bhagbanda-clean-874960365796.us-central1.run.app"
    username = "admin"
    
    print(f"🔧 Making user '{username}' an admin...")
    success = make_admin_via_api(service_url, username)
    
    if success:
        print("✅ Process completed!")
    else:
        print("❌ Process failed!")
