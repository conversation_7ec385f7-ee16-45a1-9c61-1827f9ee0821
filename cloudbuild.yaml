steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build', 
      '-t', 'gcr.io/$PROJECT_ID/bhagbanda:persistent-db',
      '.'
    ]
  
  # Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'push', 
      'gcr.io/$PROJECT_ID/bhagbanda:persistent-db'
    ]
  
  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'bhagbanda-admin',
      '--image', 'gcr.io/$PROJECT_ID/bhagbanda:persistent-db',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--allow-unauthenticated',
      '--port', '8000',
      '--set-env-vars', 'BACKUP_BUCKET=bhagbanda-db-backup,DATABASE_URL=sqlite:///./data/bhagbanda.db'
    ]

# Store the built image
images:
  - 'gcr.io/$PROJECT_ID/bhagbanda:persistent-db'

options:
  logging: CLOUD_LOGGING_ONLY
