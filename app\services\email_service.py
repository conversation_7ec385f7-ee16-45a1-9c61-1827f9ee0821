"""
Email service for sending notifications and system emails.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional
import logging
from datetime import datetime

from app.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails using SMTP."""
    
    def __init__(self):
        # Email configuration
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.sender_email = "<EMAIL>"
        self.sender_name = "Bhagbanda"
        
        # For development/testing, we'll use a Gmail account
        # In production, you'd use a proper email service like SendGrid, AWS SES, etc.
        self.smtp_username = "<EMAIL>"  # You'll need to create this
        self.smtp_password = "your-app-password"  # You'll need to set this
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        html_body: str,
        text_body: Optional[str] = None
    ) -> bool:
        """
        Send an email to a recipient.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_body: HTML content of the email
            text_body: Plain text content (optional)
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = f"{self.sender_name} <{self.sender_email}>"
            msg["To"] = to_email
            
            # Add text part if provided
            if text_body:
                text_part = MIMEText(text_body, "plain")
                msg.attach(text_part)
            
            # Add HTML part
            html_part = MIMEText(html_body, "html")
            msg.attach(html_part)
            
            # Create secure connection and send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_username, self.smtp_password)
                server.sendmail(self.sender_email, to_email, msg.as_string())
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False
    
    def send_group_invitation_email(
        self,
        user_email: str,
        user_name: str,
        group_name: str,
        inviter_name: str,
        group_id: int
    ) -> bool:
        """
        Send email notification when a user is added to a group.
        
        Args:
            user_email: Email of the user added to the group
            user_name: Name of the user added to the group
            group_name: Name of the group
            inviter_name: Name of the person who added them
            group_id: ID of the group
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = f"You've been added to '{group_name}' on Bhagbanda"
        
        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"
        group_url = f"{app_url}/groups/{group_id}"
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>You've been added to a group</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .highlight {{ background-color: #EEF2FF; padding: 15px; border-radius: 6px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome to {group_name}!</h1>
                </div>
                <div class="content">
                    <p>Hi {user_name},</p>
                    
                    <p><strong>{inviter_name}</strong> has added you to the group <strong>"{group_name}"</strong> on Bhagbanda!</p>
                    
                    <div class="highlight">
                        <h3>What's Bhagbanda?</h3>
                        <p>Bhagbanda is a group expense tracking app that helps you split bills, track shared expenses, and settle up with friends easily.</p>
                    </div>
                    
                    <p>Now you can:</p>
                    <ul>
                        <li>📝 Add and track shared expenses</li>
                        <li>💰 See who owes what to whom</li>
                        <li>🔄 Split bills automatically</li>
                        <li>💳 Record settlements and payments</li>
                    </ul>
                    
                    <div style="text-align: center;">
                        <a href="{group_url}" class="button">View Group</a>
                    </div>
                    
                    <p>If you don't have an account yet, you can sign up when you visit the link above.</p>
                    
                    <p>Happy expense tracking!</p>
                    
                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because you were added to a group on Bhagbanda.</p>
                    <p>If you believe this was sent in error, please contact us.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
        Hi {user_name},
        
        {inviter_name} has added you to the group "{group_name}" on Bhagbanda!
        
        Bhagbanda is a group expense tracking app that helps you split bills, track shared expenses, and settle up with friends easily.
        
        Now you can:
        - Add and track shared expenses
        - See who owes what to whom
        - Split bills automatically
        - Record settlements and payments
        
        Visit your group: {group_url}
        
        If you don't have an account yet, you can sign up when you visit the link above.
        
        Happy expense tracking!
        
        Best regards,
        The Bhagbanda Team
        
        ---
        This email was sent because you were added to a group on Bhagbanda.
        If you believe this was sent in error, please contact us.
        """
        
        return self.send_email(user_email, subject, html_body, text_body)
    
    def send_password_reset_email(
        self,
        user_email: str,
        reset_token: str
    ) -> bool:
        """
        Send password reset email.
        
        Args:
            user_email: Email of the user requesting password reset
            reset_token: Reset token for password reset
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = "Reset Your Bhagbanda Password"
        
        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"
        reset_url = f"{app_url}/reset-password?token={reset_token}"
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background-color: #FEF3C7; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #F59E0B; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Reset Your Password</h1>
                </div>
                <div class="content">
                    <p>You requested to reset your password for your Bhagbanda account.</p>
                    
                    <p>Click the button below to reset your password:</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_url}" class="button">Reset Password</a>
                    </div>
                    
                    <p>Or copy and paste this link in your browser:</p>
                    <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 4px;">{reset_url}</p>
                    
                    <div class="warning">
                        <p><strong>⚠️ Important:</strong></p>
                        <ul>
                            <li>This link will expire in 1 hour</li>
                            <li>The link can only be used once</li>
                            <li>If you didn't request this, please ignore this email</li>
                        </ul>
                    </div>
                    
                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because a password reset was requested for your account.</p>
                    <p>If you didn't request this, please ignore this email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
        Reset Your Bhagbanda Password
        
        You requested to reset your password for your Bhagbanda account.
        
        Click this link to reset your password:
        {reset_url}
        
        Important:
        - This link will expire in 1 hour
        - The link can only be used once
        - If you didn't request this, please ignore this email
        
        Best regards,
        The Bhagbanda Team
        
        ---
        This email was sent because a password reset was requested for your account.
        If you didn't request this, please ignore this email.
        """
        
        return self.send_email(user_email, subject, html_body, text_body)


# Global email service instance
email_service = EmailService()
