"""
Email service for sending notifications and system emails.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional
import logging
import os
from datetime import datetime

from app.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails using SMTP."""
    
    def __init__(self):
        # Email configuration from environment variables
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.sendgrid.net")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.sender_email = os.getenv("SENDER_EMAIL", "<EMAIL>")
        self.sender_name = os.getenv("SENDER_NAME", "Bhagbanda")

        # SMTP credentials from environment variables
        self.smtp_username = os.getenv("SMTP_USERNAME", "apikey")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")

        # For testing, use a working test configuration
        if not self.smtp_password:
            # Use Gmail as fallback for testing
            self.smtp_server = "smtp.gmail.com"
            self.smtp_port = 587
            # You need to set these for testing
            test_email = os.getenv("TEST_EMAIL", "")
            test_password = os.getenv("TEST_PASSWORD", "")

            if test_email and test_password:
                self.smtp_username = test_email
                self.smtp_password = test_password
                logger.info("Using test Gmail configuration")
            else:
                logger.warning("No email configuration found")

        # Check if email is configured
        self.is_configured = bool(self.smtp_username and self.smtp_password)

        if not self.is_configured:
            logger.warning("Email service not configured.")
            logger.warning("Options to enable emails:")
            logger.warning("1. SendGrid: Set SMTP_PASSWORD to your SendGrid API key")
            logger.warning("2. Gmail: Set TEST_EMAIL and TEST_PASSWORD environment variables")
        else:
            logger.info(f"Email service configured with {self.smtp_server}:{self.smtp_port}")
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        html_body: str,
        text_body: Optional[str] = None
    ) -> bool:
        """
        Send an email to a recipient.

        Args:
            to_email: Recipient email address
            subject: Email subject
            html_body: HTML content of the email
            text_body: Plain text content (optional)

        Returns:
            True if email was sent successfully, False otherwise
        """
        # Check if email is configured
        if not self.is_configured:
            logger.warning(f"Email not configured. Would send email to {to_email} with subject: {subject}")
            return False

        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = f"{self.sender_name} <{self.sender_email}>"
            msg["To"] = to_email
            
            # Add text part if provided
            if text_body:
                text_part = MIMEText(text_body, "plain")
                msg.attach(text_part)
            
            # Add HTML part
            html_part = MIMEText(html_body, "html")
            msg.attach(html_part)
            
            # Create secure connection and send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_username, self.smtp_password)
                server.sendmail(self.sender_email, to_email, msg.as_string())
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False
    
    def send_group_invitation_email(
        self,
        user_email: str,
        user_name: str,
        group_name: str,
        inviter_name: str,
        group_id: int
    ) -> bool:
        """
        Send email notification when a user is added to a group.
        
        Args:
            user_email: Email of the user added to the group
            user_name: Name of the user added to the group
            group_name: Name of the group
            inviter_name: Name of the person who added them
            group_id: ID of the group
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = f"You've been added to '{group_name}' on Bhagbanda"
        
        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"
        group_url = f"{app_url}/groups/{group_id}"
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>You've been added to a group</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .highlight {{ background-color: #EEF2FF; padding: 15px; border-radius: 6px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome to {group_name}!</h1>
                </div>
                <div class="content">
                    <p>Hi {user_name},</p>
                    
                    <p><strong>{inviter_name}</strong> has added you to the group <strong>"{group_name}"</strong> on Bhagbanda!</p>
                    
                    <div class="highlight">
                        <h3>What's Bhagbanda?</h3>
                        <p>Bhagbanda is a group expense tracking app that helps you split bills, track shared expenses, and settle up with friends easily.</p>
                    </div>
                    
                    <p>Now you can:</p>
                    <ul>
                        <li>📝 Add and track shared expenses</li>
                        <li>💰 See who owes what to whom</li>
                        <li>🔄 Split bills automatically</li>
                        <li>💳 Record settlements and payments</li>
                    </ul>
                    
                    <div style="text-align: center;">
                        <a href="{group_url}" class="button">View Group</a>
                    </div>
                    
                    <p>If you don't have an account yet, you can sign up when you visit the link above.</p>
                    
                    <p>Happy expense tracking!</p>
                    
                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because you were added to a group on Bhagbanda.</p>
                    <p>If you believe this was sent in error, please contact us.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
        Hi {user_name},
        
        {inviter_name} has added you to the group "{group_name}" on Bhagbanda!
        
        Bhagbanda is a group expense tracking app that helps you split bills, track shared expenses, and settle up with friends easily.
        
        Now you can:
        - Add and track shared expenses
        - See who owes what to whom
        - Split bills automatically
        - Record settlements and payments
        
        Visit your group: {group_url}
        
        If you don't have an account yet, you can sign up when you visit the link above.
        
        Happy expense tracking!
        
        Best regards,
        The Bhagbanda Team
        
        ---
        This email was sent because you were added to a group on Bhagbanda.
        If you believe this was sent in error, please contact us.
        """
        
        return self.send_email(user_email, subject, html_body, text_body)
    
    def send_password_reset_email(
        self,
        user_email: str,
        reset_token: str
    ) -> bool:
        """
        Send password reset email.
        
        Args:
            user_email: Email of the user requesting password reset
            reset_token: Reset token for password reset
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = "Reset Your Bhagbanda Password"
        
        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"
        reset_url = f"{app_url}/reset-password?token={reset_token}"
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background-color: #FEF3C7; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #F59E0B; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Reset Your Password</h1>
                </div>
                <div class="content">
                    <p>You requested to reset your password for your Bhagbanda account.</p>
                    
                    <p>Click the button below to reset your password:</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_url}" class="button">Reset Password</a>
                    </div>
                    
                    <p>Or copy and paste this link in your browser:</p>
                    <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 4px;">{reset_url}</p>
                    
                    <div class="warning">
                        <p><strong>⚠️ Important:</strong></p>
                        <ul>
                            <li>This link will expire in 1 hour</li>
                            <li>The link can only be used once</li>
                            <li>If you didn't request this, please ignore this email</li>
                        </ul>
                    </div>
                    
                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because a password reset was requested for your account.</p>
                    <p>If you didn't request this, please ignore this email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
        Reset Your Bhagbanda Password
        
        You requested to reset your password for your Bhagbanda account.
        
        Click this link to reset your password:
        {reset_url}
        
        Important:
        - This link will expire in 1 hour
        - The link can only be used once
        - If you didn't request this, please ignore this email
        
        Best regards,
        The Bhagbanda Team
        
        ---
        This email was sent because a password reset was requested for your account.
        If you didn't request this, please ignore this email.
        """
        
        return self.send_email(user_email, subject, html_body, text_body)

    def send_email_verification_email(
        self,
        user_email: str,
        user_name: str,
        verification_token: str
    ) -> bool:
        """
        Send email verification email to new user.

        Args:
            user_email: Email of the new user
            user_name: Name of the new user
            verification_token: Verification token for email confirmation

        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = "Welcome to Bhagbanda! Please verify your email"

        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"
        verification_url = f"{app_url}/verify-email?token={verification_token}"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Bhagbanda</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .highlight {{ background-color: #EEF2FF; padding: 15px; border-radius: 6px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome to Bhagbanda!</h1>
                </div>
                <div class="content">
                    <p>Hi {user_name},</p>

                    <p>Welcome to Bhagbanda! We're excited to have you join our community of smart expense trackers.</p>

                    <div class="highlight">
                        <h3>What's Bhagbanda?</h3>
                        <p>Bhagbanda is your go-to app for tracking group expenses, splitting bills, and settling up with friends. No more awkward money conversations!</p>
                    </div>

                    <p>To get started, please verify your email address by clicking the button below:</p>

                    <div style="text-align: center;">
                        <a href="{verification_url}" class="button">Verify Email Address</a>
                    </div>

                    <p>Or copy and paste this link in your browser:</p>
                    <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 4px;">{verification_url}</p>

                    <p>Once verified, you'll be able to:</p>
                    <ul>
                        <li>📝 Create and join expense groups</li>
                        <li>💰 Track shared expenses and splits</li>
                        <li>🔄 See who owes what to whom</li>
                        <li>💳 Record settlements and payments</li>
                        <li>📧 Get notifications for group activities</li>
                    </ul>

                    <p><strong>Important:</strong> This verification link will expire in 24 hours for security reasons.</p>

                    <p>If you didn't create an account with us, please ignore this email.</p>

                    <p>Happy expense tracking!</p>

                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because an account was created with this email address.</p>
                    <p>If you believe this was sent in error, please contact us.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        Welcome to Bhagbanda!

        Hi {user_name},

        Welcome to Bhagbanda! We're excited to have you join our community of smart expense trackers.

        What's Bhagbanda?
        Bhagbanda is your go-to app for tracking group expenses, splitting bills, and settling up with friends. No more awkward money conversations!

        To get started, please verify your email address by clicking this link:
        {verification_url}

        Once verified, you'll be able to:
        - Create and join expense groups
        - Track shared expenses and splits
        - See who owes what to whom
        - Record settlements and payments
        - Get notifications for group activities

        Important: This verification link will expire in 24 hours for security reasons.

        If you didn't create an account with us, please ignore this email.

        Happy expense tracking!

        Best regards,
        The Bhagbanda Team

        ---
        This email was sent because an account was created with this email address.
        If you believe this was sent in error, please contact us.
        """

        return self.send_email(user_email, subject, html_body, text_body)

    def send_settlement_notification_email(
        self,
        payee_email: str,
        payee_name: str,
        payer_name: str,
        amount: float,
        payment_method: str = None,
        description: str = None,
        group_name: str = None
    ) -> bool:
        """
        Send email notification when someone settles up with you.

        Args:
            payee_email: Email of the person who received the payment
            payee_name: Name of the person who received the payment
            payer_name: Name of the person who made the payment
            amount: Amount that was paid
            payment_method: How the payment was made (Cash, Venmo, etc.)
            description: Optional description of the settlement
            group_name: Name of the group (if applicable)

        Returns:
            True if email was sent successfully, False otherwise
        """
        subject = f"💰 {payer_name} settled up with you on Bhagbanda"

        # App URL
        app_url = "https://bhagbanda-admin-************.us-central1.run.app"

        # Format amount
        amount_str = f"${amount:.2f}"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Settlement Notification</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #10B981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .amount {{ font-size: 24px; font-weight: bold; color: #10B981; text-align: center; margin: 20px 0; }}
                .details {{ background-color: #EEF2FF; padding: 15px; border-radius: 6px; margin: 15px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .button {{ display: inline-block; background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>💰 Payment Received!</h1>
                </div>
                <div class="content">
                    <p>Hi {payee_name},</p>

                    <p><strong>{payer_name}</strong> has settled up with you!</p>

                    <div class="amount">{amount_str}</div>

                    <div class="details">
                        <h3>Settlement Details:</h3>
                        <p><strong>From:</strong> {payer_name}</p>
                        <p><strong>To:</strong> {payee_name}</p>
                        <p><strong>Amount:</strong> {amount_str}</p>
                        {f'<p><strong>Payment Method:</strong> {payment_method}</p>' if payment_method else ''}
                        {f'<p><strong>Group:</strong> {group_name}</p>' if group_name else ''}
                        {f'<p><strong>Description:</strong> {description}</p>' if description else ''}
                    </div>

                    <p>This settlement has been recorded in your Bhagbanda account and your balances have been updated accordingly.</p>

                    <div style="text-align: center;">
                        <a href="{app_url}" class="button">View Your Account</a>
                    </div>

                    <p>Keep track of all your settlements and balances on Bhagbanda!</p>

                    <p>Best regards,<br>The Bhagbanda Team</p>
                </div>
                <div class="footer">
                    <p>This email was sent because a settlement was recorded involving your account.</p>
                    <p>If you believe this was sent in error, please contact us.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_body = f"""
        Payment Received!

        Hi {payee_name},

        {payer_name} has settled up with you!

        Settlement Details:
        - From: {payer_name}
        - To: {payee_name}
        - Amount: {amount_str}
        {f'- Payment Method: {payment_method}' if payment_method else ''}
        {f'- Group: {group_name}' if group_name else ''}
        {f'- Description: {description}' if description else ''}

        This settlement has been recorded in your Bhagbanda account and your balances have been updated accordingly.

        View your account: {app_url}

        Keep track of all your settlements and balances on Bhagbanda!

        Best regards,
        The Bhagbanda Team

        ---
        This email was sent because a settlement was recorded involving your account.
        If you believe this was sent in error, please contact us.
        """

        return self.send_email(payee_email, subject, html_body, text_body)


def send_settlement_notification_email(
    payee_email: str,
    payee_name: str,
    payer_name: str,
    amount: float,
    payment_method: str = None,
    description: str = None,
    group_name: str = None
):
    """Helper function for background tasks."""
    return email_service.send_settlement_notification_email(
        payee_email=payee_email,
        payee_name=payee_name,
        payer_name=payer_name,
        amount=amount,
        payment_method=payment_method,
        description=description,
        group_name=group_name
    )


# Global email service instance
email_service = EmailService()
