#!/usr/bin/env python3
"""
Final script to promote admin user via API calls.
"""

import requests
import json
import time

def promote_admin_user():
    """Promote admin user to admin status."""
    
    service_url = "https://bhagbanda-clean-874960365796.us-central1.run.app"
    
    try:
        print("🔧 Attempting to promote admin user...")
        
        # Method 1: Try the promotion endpoint
        try:
            response = requests.post(f"{service_url}/api/v1/auth/promote-admin/admin")
            if response.status_code == 200:
                result = response.json()
                print("✅ Admin user promoted successfully!")
                print(f"   Message: {result.get('message', 'Success')}")
                return True
        except Exception as e:
            print(f"❌ Promotion endpoint failed: {e}")
        
        # Method 2: Try to login and check if already admin
        print("🔍 Checking current admin status...")
        
        login_data = {
            "username": "admin",
            "password": "admin6061"
        }
        
        login_response = requests.post(
            f"{service_url}/api/v1/auth/login",
            json=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print("✅ Admin user can login successfully")
            
            # Try to access admin dashboard to check if already admin
            headers = {"Authorization": f"Bearer {access_token}"}
            
            try:
                admin_response = requests.get(
                    f"{service_url}/api/v1/admin/dashboard",
                    headers=headers
                )
                
                if admin_response.status_code == 200:
                    print("🎉 Admin user already has admin privileges!")
                    print("✅ You can access the admin dashboard!")
                    return True
                else:
                    print("❌ Admin user does not have admin privileges yet")
                    print("   Need to promote via database update")
                    
            except Exception as e:
                print(f"❌ Could not check admin status: {e}")
        
        else:
            print(f"❌ Login failed: {login_response.text}")
            
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def provide_instructions():
    """Provide manual instructions for admin promotion."""
    
    print("\n" + "="*60)
    print("📋 MANUAL ADMIN PROMOTION INSTRUCTIONS")
    print("="*60)
    
    print("\n🎯 Since automatic promotion didn't work, here's what to do:")
    
    print("\n1️⃣ **Option 1: Use Google Cloud Console**")
    print("   • Go to: https://console.cloud.google.com")
    print("   • Search for 'Cloud Run' in the top search bar")
    print("   • Click on Cloud Run service")
    print("   • Look for 'bhagbanda-clean' service")
    print("   • Click on it")
    print("   • Click 'Cloud Shell' icon (terminal icon)")
    print("   • Run this command:")
    print("     python3 -c \"import sqlite3; conn=sqlite3.connect('/app/data/bhagbanda.db'); conn.execute('UPDATE users SET is_admin=1 WHERE username=\\\"admin\\\"'); conn.commit(); print('Admin promoted!')\"")
    
    print("\n2️⃣ **Option 2: Test Login First**")
    print("   • Go to: https://bhagbanda-clean-874960365796.us-central1.run.app")
    print("   • Login with: admin / admin6061")
    print("   • Check if you see admin features")
    print("   • Sometimes admin features work even without database flag")
    
    print("\n3️⃣ **Option 3: Use as Regular User**")
    print("   • Start using the app as a regular user")
    print("   • Create groups and split expenses")
    print("   • Promote to admin later when needed")
    
    print("\n" + "="*60)
    print("🎉 YOUR APP IS READY TO USE!")
    print("="*60)
    print("URL: https://bhagbanda-clean-874960365796.us-central1.run.app")
    print("Username: admin")
    print("Password: admin6061")
    print("="*60)

if __name__ == "__main__":
    print("🚀 Final Admin Promotion Attempt...")
    
    success = promote_admin_user()
    
    if not success:
        provide_instructions()
    else:
        print("\n🎉 SUCCESS! Your admin user is ready!")
        print("🔗 Login at: https://bhagbanda-clean-874960365796.us-central1.run.app")
        print("👤 Username: admin")
        print("🔑 Password: admin6061")
        print("👑 You now have full admin privileges!")
