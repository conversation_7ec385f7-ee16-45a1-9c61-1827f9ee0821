# 🔒 Security Setup Guide

**IMPORTANT: Complete these security steps before deploying to production!**

## 🚨 **Critical Security Steps**

### 1. **Change Default Passwords**

The application uses environment variables for sensitive configuration. **Never use the default values in production!**

#### **Admin Password**
```bash
# Set a strong admin password
export ADMIN_PASSWORD="your-super-strong-admin-password-here"

# Or in your .env file:
ADMIN_PASSWORD=your-super-strong-admin-password-here
```

#### **JWT Secret Key**
```bash
# Generate a secure random secret key (32+ characters)
export SECRET_KEY="$(openssl rand -hex 32)"

# Or manually set a strong secret:
export SECRET_KEY="your-super-secret-jwt-key-32-characters-minimum"
```

### 2. **Environment Variables Setup**

Create a `.env` file with secure values:

```env
# Database Configuration
DATABASE_URL=sqlite:///./data/bhagbanda.db

# JWT Configuration (CRITICAL: Change these!)
SECRET_KEY=your-super-secret-jwt-key-32-characters-minimum-change-this
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Admin Configuration (CRITICAL: Change this!)
ADMIN_PASSWORD=your-super-strong-admin-password-change-this

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=Bhagbanda Backend
VERSION=1.0.0

# CORS Configuration
ALLOWED_ORIGINS=https://your-domain.com

# Production Configuration
DEBUG=false
```

### 3. **Google Cloud Deployment Security**

When deploying to Google Cloud Run, set environment variables securely:

```bash
gcloud run deploy bhagbanda-admin \
  --image=your-image \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --port=8000 \
  --set-env-vars="SECRET_KEY=your-secure-secret-key,ADMIN_PASSWORD=your-secure-admin-password,DEBUG=false"
```

### 4. **Database Security**

- **SQLite**: Database file is automatically backed up to Google Cloud Storage
- **PostgreSQL**: Use Cloud SQL with strong passwords and SSL connections
- **Backups**: Automatic backups are configured for data persistence

### 5. **Access Control**

#### **Admin Access**
1. **Login** with admin credentials:
   - Username: `admin`
   - Password: `[value of ADMIN_PASSWORD environment variable]`

2. **Create additional admin users** through the admin panel
3. **Remove default admin** after creating your own admin accounts

#### **User Management**
- All user passwords are hashed using bcrypt
- JWT tokens expire after configured time (default: 30 minutes)
- Admin users have full system access

## ✅ **Security Checklist**

Before going to production, verify:

- [ ] **Changed ADMIN_PASSWORD** from default value
- [ ] **Changed SECRET_KEY** to random 32+ character string
- [ ] **Set DEBUG=false** in production
- [ ] **Configured CORS** for your domain only
- [ ] **Database backups** are working
- [ ] **HTTPS** is enabled (automatic with Google Cloud Run)
- [ ] **Environment variables** are set securely
- [ ] **No hardcoded passwords** in code
- [ ] **Strong admin passwords** for all admin users

## 🛡️ **Default Values (DO NOT USE IN PRODUCTION)**

These are the default values that **MUST** be changed:

```
❌ ADMIN_PASSWORD=change-this-admin-password-in-production
❌ SECRET_KEY=your-secret-key-change-in-production
❌ DEBUG=true
```

## 🎯 **Quick Security Setup**

1. **Copy environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Generate secure secret key:**
   ```bash
   openssl rand -hex 32
   ```

3. **Edit .env file** with your secure values

4. **Deploy with secure environment variables**

5. **Test admin login** with your new password

6. **Create additional admin users** as needed

Your application is now secure and ready for production use! 🚀
