"""
Group management router for creating and managing expense groups.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List

from app.database import get_db
from app.models.user import User
from app.models.group import Group, GroupMember
from app.models.expense import Expense
from app.models.expense import ExpenseSplit
from app.models.settlement import Settlement
from app.schemas.group import (
    GroupCreate, GroupUpdate, GroupResponse, GroupMemberAdd,
    GroupListResponse, GroupMemberResponse
)
from app.auth import get_current_active_user
from app.services.email_service import email_service

router = APIRouter(prefix="/groups", tags=["Groups"])


@router.post("/", response_model=GroupResponse, status_code=status.HTTP_201_CREATED)
def create_group(
    group_data: GroupCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new expense group.
    
    The current user becomes the creator and is automatically added as a member.
    """
    # Create the group
    db_group = Group(
        name=group_data.name,
        description=group_data.description,
        creator_id=current_user.id
    )
    
    db.add(db_group)
    db.commit()
    db.refresh(db_group)
    
    # Add creator as a member
    group_member = GroupMember(
        group_id=db_group.id,
        user_id=current_user.id
    )
    
    db.add(group_member)
    db.commit()
    
    # Return group with creator info
    db.refresh(db_group)
    return db_group


@router.get("/", response_model=List[GroupResponse])
def get_my_groups(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all groups where the current user is a member.
    
    Returns groups with basic information and member count.
    """
    # Query groups where user is a member
    groups = db.query(Group).join(GroupMember).filter(
        GroupMember.user_id == current_user.id
    ).all()
    
    # Add member count to each group
    for group in groups:
        group.member_count = len(group.members)
    
    return groups


@router.get("/{group_id}", response_model=GroupResponse)
def get_group(
    group_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific group.
    
    Includes group details, creator info, and member list.
    User must be a member of the group to access this information.
    """
    # Check if user is a member of the group
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get group with related data
    group = db.query(Group).filter(Group.id == group_id).first()

    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    # Get all members for this group
    members = db.query(GroupMember).filter(GroupMember.group_id == group_id).all()

    # Set member count
    group.member_count = len(members)

    return group


@router.put("/{group_id}", response_model=GroupResponse)
def update_group(
    group_id: int,
    group_update: GroupUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update group information.
    
    Only the group creator can update group details.
    """
    group = db.query(Group).filter(Group.id == group_id).first()
    
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Check if user is the creator
    if group.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the group creator can update group details"
        )
    
    # Update fields if provided
    if group_update.name is not None:
        group.name = group_update.name
    if group_update.description is not None:
        group.description = group_update.description
    
    db.commit()
    db.refresh(group)
    
    return group


@router.post("/{group_id}/members", response_model=GroupMemberResponse, status_code=status.HTTP_201_CREATED)
def add_group_member(
    group_id: int,
    member_data: GroupMemberAdd,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Add a new member to the group.
    
    Any group member can add new members.
    """
    # Check if current user is a member of the group
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Check if the user to be added exists
    user_to_add = db.query(User).filter(User.id == member_data.user_id).first()
    if not user_to_add:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check if user is already a member
    existing_membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == member_data.user_id
    ).first()
    
    if existing_membership:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already a member of this group"
        )
    
    # Add the new member
    new_member = GroupMember(
        group_id=group_id,
        user_id=member_data.user_id
    )

    db.add(new_member)
    db.commit()
    db.refresh(new_member)

    # Get group information for email
    group = db.query(Group).filter(Group.id == group_id).first()

    # Send email notification to the new member
    if user_to_add.email and group:
        background_tasks.add_task(
            email_service.send_group_invitation_email,
            user_email=user_to_add.email,
            user_name=user_to_add.full_name or user_to_add.username,
            group_name=group.name,
            inviter_name=current_user.full_name or current_user.username,
            group_id=group_id
        )

    return new_member


@router.delete("/{group_id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def remove_group_member(
    group_id: int,
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Remove a member from the group.
    
    Users can remove themselves, or the group creator can remove any member.
    """
    # Check if current user is a member of the group
    current_membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not current_membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get the group to check creator
    group = db.query(Group).filter(Group.id == group_id).first()
    
    # Check permissions: user can remove themselves, or creator can remove anyone
    if user_id != current_user.id and group.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only remove yourself or you must be the group creator"
        )
    
    # Find the membership to remove
    membership_to_remove = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == user_id
    ).first()
    
    if not membership_to_remove:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User is not a member of this group"
        )
    
    # Don't allow creator to remove themselves if there are other members
    if user_id == group.creator_id:
        other_members = db.query(GroupMember).filter(
            GroupMember.group_id == group_id,
            GroupMember.user_id != user_id
        ).count()
        
        if other_members > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Group creator cannot leave while there are other members. Transfer ownership or remove all members first."
            )
    
    db.delete(membership_to_remove)
    db.commit()


@router.get("/{group_id}/members", response_model=List[GroupMemberResponse])
def get_group_members(
    group_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all members of a group.
    
    User must be a member of the group to see the member list.
    """
    # Check if user is a member of the group
    membership = db.query(GroupMember).filter(
        GroupMember.group_id == group_id,
        GroupMember.user_id == current_user.id
    ).first()
    
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get all group members
    members = db.query(GroupMember).filter(GroupMember.group_id == group_id).all()

    return members


@router.delete("/{group_id}")
def delete_group(
    group_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a group (creator only).

    Only the user who created the group can delete it.
    This will permanently delete:
    1. All expenses in the group
    2. All expense splits
    3. All group memberships
    4. All settlements
    5. The group itself
    """
    # Get the group
    group = db.query(Group).filter(Group.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    # Check if current user is the creator
    if group.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the group creator can delete this group"
        )

    # Delete all related data in the correct order

    # 1. Delete all expense splits first
    expenses = db.query(Expense).filter(Expense.group_id == group_id).all()
    for expense in expenses:
        db.query(ExpenseSplit).filter(ExpenseSplit.expense_id == expense.id).delete()

    # 2. Delete all expenses
    db.query(Expense).filter(Expense.group_id == group_id).delete()

    # 3. Delete all settlements
    db.query(Settlement).filter(Settlement.group_id == group_id).delete()

    # 4. Delete all group memberships
    db.query(GroupMember).filter(GroupMember.group_id == group_id).delete()

    # 5. Delete the group itself
    group_name = group.name
    db.delete(group)
    db.commit()

    return {"message": f"Group '{group_name}' and all its data have been deleted successfully"}
