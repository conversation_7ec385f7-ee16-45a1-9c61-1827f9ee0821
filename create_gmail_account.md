# 📧 Create Gmail Account for Bhagbanda

To enable email functionality, you need to create a Gmail account and configure it properly.

## 🚀 **Quick Setup (5 minutes)**

### **Step 1: Create Gmail Account**
1. Go to https://accounts.google.com/signup
2. Create account: `<EMAIL>` (or similar)
3. Complete the signup process

### **Step 2: Enable 2-Factor Authentication**
1. Go to https://myaccount.google.com/security
2. Click "2-Step Verification"
3. Follow the setup process (use your phone number)

### **Step 3: Generate App Password**
1. Go to https://myaccount.google.com/apppasswords
2. Select "Mail" as the app
3. Click "Generate"
4. Copy the 16-character password (e.g., `abcd efgh ijkl mnop`)
5. **Important**: Remove spaces, so it becomes `abcdefghijklmnop`

### **Step 4: Test the Configuration**
```bash
# Set environment variables
export SMTP_USERNAME="<EMAIL>"
export SMTP_PASSWORD="abcdefghijklmnop"

# Test email
python test_email.py
```

### **Step 5: Deploy with Email**
```bash
# Build and deploy with email configuration
docker build -t ssshakya/bhagbanda:with-email .
docker push ssshakya/bhagbanda:with-email

gcloud run deploy bhagbanda-admin \
  --image=ssshakya/bhagbanda:with-email \
  --set-env-vars="SMTP_USERNAME=<EMAIL>,SMTP_PASSWORD=abcdefghijklmnop,BACKUP_BUCKET=bhagbanda-db-backup,DATABASE_URL=sqlite:///./data/bhagbanda.db,SECRET_KEY=bhagbanda-production-secret-key-32-chars-random-2024,ADMIN_PASSWORD=bhagbanda-admin-secure-password-2024,DEBUG=false"
```

## 🧪 **Test Email Functionality**

Once deployed with email configuration:

### **Test Group Invitations:**
1. Login to the app
2. Create a group or go to existing group
3. Add a member (use a real email address)
4. Check if the member receives an invitation email

### **Test Password Reset:**
1. Go to login page
2. Click "Forgot your password?"
3. Enter email address
4. Check if reset email is received

## 🔧 **Alternative: Use Your Own Gmail**

If you have a Gmail account:

1. **Enable 2FA** on your existing Gmail
2. **Generate App Password** for "Mail"
3. **Use your credentials**:
   ```bash
   export SMTP_USERNAME="<EMAIL>"
   export SMTP_PASSWORD="your-app-password"
   ```

## 🆘 **Troubleshooting**

### **Common Issues:**

1. **"Authentication failed"**
   - Make sure 2FA is enabled
   - Use App Password, not regular password
   - Remove spaces from App Password

2. **"Less secure app access"**
   - This should be disabled (use App Passwords instead)
   - Don't enable "Less secure app access"

3. **"Invalid credentials"**
   - Double-check the App Password
   - Make sure it's 16 characters with no spaces
   - Try generating a new App Password

### **Test Commands:**
```bash
# Test SMTP connection
python -c "
import smtplib
server = smtplib.SMTP('smtp.gmail.com', 587)
server.starttls()
server.login('<EMAIL>', 'your-app-password')
print('✅ SMTP connection successful!')
server.quit()
"
```

## 📝 **Current Status**

The email service is ready and will work once you:

1. ✅ **Create Gmail account** with 2FA
2. ✅ **Generate App Password**
3. ✅ **Deploy with credentials**
4. ✅ **Test functionality**

**The app is configured to send emails for:**
- 📧 Group member invitations
- 🔐 Password reset links
- 📱 Professional HTML email templates

**Just need the Gmail credentials to make it work!**
