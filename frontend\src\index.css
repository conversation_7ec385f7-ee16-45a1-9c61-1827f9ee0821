@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile responsive fixes */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: auto;
  max-width: 100vw;
}

/* Prevent horizontal overflow */
.container {
  max-width: 100%;
  overflow-x: auto;
}

/* Table responsive wrapper */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Card responsive fixes */
.card-responsive {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Button responsive fixes */
.btn-responsive {
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Custom styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-danger-600 hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-danger-500;
  }
  
  .input-field {
    @apply appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm;
  }
  
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }
  
  .card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-4 py-5 sm:p-6;
  }

  /* Mobile-specific improvements */
  .mobile-modal {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .mobile-modal-content {
    @apply w-full max-w-full sm:max-w-2xl mx-auto;
  }

  .mobile-form-section {
    @apply space-y-4 sm:space-y-6;
  }

  .mobile-button-group {
    @apply flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3;
  }

  .mobile-input-group {
    @apply flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2;
  }
}
