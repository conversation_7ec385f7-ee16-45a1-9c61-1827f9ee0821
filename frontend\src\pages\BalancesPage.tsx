/**
 * Balances Page Component
 * Shows overall balance summary and settlement suggestions
 */

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { BarChart3, TrendingUp, TrendingDown, Users, CheckCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { balanceService } from '../services/balanceService';
import { BalanceChart } from '../components/balances/BalanceChart';
import { SettleUpButton } from '../components/settlements/SettleUpButton';

export const BalancesPage: React.FC = () => {
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const queryClient = useQueryClient();

  // Fetch overall balance
  const { data: overallBalance, isLoading: overallLoading, refetch: refetchOverallBalance } = useQuery({
    queryKey: ['overall-balance'],
    queryFn: balanceService.getOverallBalance,
    staleTime: 0, // Always consider data stale to force refresh
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  // Auto-refresh balances when component mounts or becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refetchOverallBalance();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [refetchOverallBalance]);

  // Fetch settlement suggestions for selected group
  const { data: suggestions = [], isLoading: suggestionsLoading } = useQuery({
    queryKey: ['settlement-suggestions', selectedGroupId],
    queryFn: () => balanceService.getSettlementSuggestions(selectedGroupId!),
    enabled: !!selectedGroupId,
    staleTime: 0, // Always consider data stale
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });

  // Create settlement mutation
  const createSettlementMutation = useMutation({
    mutationFn: balanceService.createSettlement,
    onSuccess: () => {
      // Invalidate and refetch all balance-related queries
      queryClient.invalidateQueries({ queryKey: ['overall-balance'] });
      queryClient.invalidateQueries({ queryKey: ['settlement-suggestions'] });
      queryClient.invalidateQueries({ queryKey: ['group-balances'] });
      queryClient.invalidateQueries({ queryKey: ['group-balance'] });
      queryClient.invalidateQueries({ queryKey: ['all-expenses'] });

      // Force immediate refetch
      queryClient.refetchQueries({ queryKey: ['overall-balance'] });
      if (selectedGroupId) {
        queryClient.refetchQueries({ queryKey: ['settlement-suggestions', selectedGroupId] });
      }

      toast.success('Settlement recorded successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleSettlement = (suggestion: any) => {
    createSettlementMutation.mutate({
      payee_id: suggestion.to_user_id,
      amount: suggestion.amount,
      group_id: selectedGroupId || undefined,
      description: `Settlement from ${suggestion.from_user.full_name || suggestion.from_user.username} to ${suggestion.to_user.full_name || suggestion.to_user.username}`,
    });
  };

  if (overallLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-0">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Balances & Settlements</h1>
        <p className="mt-1 text-sm text-gray-600">
          View your balance summary and settle up with friends.
        </p>
      </div>

      {/* Overall Balance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-6 w-6 text-success-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Owed to You
                  </dt>
                  <dd className="text-lg font-medium text-success-600">
                    {formatCurrency(overallBalance?.total_owed_to_you || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-6 w-6 text-danger-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total You Owe
                  </dt>
                  <dd className="text-lg font-medium text-danger-600">
                    {formatCurrency(overallBalance?.total_you_owe || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-6 w-6 text-primary-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Net Balance
                  </dt>
                  <dd className={`text-lg font-medium ${
                    (overallBalance?.net_balance || 0) >= 0 
                      ? 'text-success-600' 
                      : 'text-danger-600'
                  }`}>
                    {formatCurrency(overallBalance?.net_balance || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Group Balances */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Balance by Group</h3>
          </div>
          <div className="card-body">
            {overallBalance?.group_balances && overallBalance.group_balances.length > 0 ? (
              <div className="space-y-4">
                {overallBalance.group_balances.map((groupBalance) => (
                  <div key={groupBalance.group_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">{groupBalance.group_name}</h4>
                      <button
                        onClick={() => setSelectedGroupId(groupBalance.group_id)}
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        View Settlements
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Owed to you:</span>
                        <p className="font-medium text-success-600">
                          {formatCurrency(groupBalance.total_owed_to_you)}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">You owe:</span>
                        <p className="font-medium text-danger-600">
                          {formatCurrency(groupBalance.total_you_owe)}
                        </p>
                      </div>
                    </div>

                    {groupBalance.balances.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Individual Balances:</h5>
                        <div className="space-y-1">
                          {groupBalance.balances.map((balance) => (
                            <div key={balance.user_id} className="flex justify-between text-sm">
                              <span className="text-gray-600">
                                {balance.user.full_name || balance.user.username}
                              </span>
                              <span className={balance.amount >= 0 ? 'text-success-600' : 'text-danger-600'}>
                                {balance.amount >= 0 ? 'owes you ' : 'you owe '}
                                {formatCurrency(Math.abs(balance.amount))}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No balances</h3>
                <p className="mt-1 text-sm text-gray-500">
                  All settled up! No outstanding balances.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Settlement Suggestions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              Settlement Suggestions
              {selectedGroupId && (
                <span className="ml-2 text-sm font-normal text-gray-500">
                  for selected group
                </span>
              )}
            </h3>
          </div>
          <div className="card-body">
            {selectedGroupId ? (
              suggestionsLoading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                </div>
              ) : suggestions.length > 0 ? (
                <div className="space-y-4">
                  {suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {suggestion.from_user.full_name || suggestion.from_user.username}
                        </p>
                        <p className="text-sm text-gray-500">
                          should pay {suggestion.to_user.full_name || suggestion.to_user.username}
                        </p>
                        <p className="text-lg font-bold text-primary-600">
                          {formatCurrency(suggestion.amount)}
                        </p>
                      </div>
                      <SettleUpButton
                        suggestion={suggestion}
                        groupId={selectedGroupId || undefined}
                        variant="primary"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <CheckCircle className="mx-auto h-12 w-12 text-success-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">All settled up!</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    No settlements needed for this group.
                  </p>
                </div>
              )
            ) : (
              <div className="text-center py-6">
                <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Select a group</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose a group from the left to see settlement suggestions.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Balance Chart */}
      {overallBalance && (
        <div className="mt-8">
          <BalanceChart data={overallBalance} />
        </div>
      )}
    </div>
  );
};
