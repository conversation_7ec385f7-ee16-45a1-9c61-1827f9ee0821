"""
Settlement router for recording debt payments between users.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List

from app.database import get_db
from app.models.user import User
from app.models.group import GroupMember
from app.models.settlement import Settlement
from app.services.balance_calculator import BalanceCalculator
from app.schemas.settlement import (
    SettlementCreate, SettlementResponse, SettlementListResponse
)
from app.auth import get_current_active_user

router = APIRouter(prefix="/settlements", tags=["Settlements"])


@router.post("/", response_model=SettlementResponse, status_code=status.HTTP_201_CREATED)
def create_settlement(
    settlement_data: SettlementCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Record a settlement (payment) between users.
    
    The current user is the payer, and they specify who they paid (payee).
    If group_id is provided, both users must be members of that group.
    """
    # Validate that payee exists
    payee = db.query(User).filter(User.id == settlement_data.payee_id).first()
    if not payee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payee user not found"
        )
    
    # Can't pay yourself
    if settlement_data.payee_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot create settlement with yourself"
        )
    
    # If group_id is specified, validate both users are members
    if settlement_data.group_id:
        payer_membership = db.query(GroupMember).filter(
            GroupMember.group_id == settlement_data.group_id,
            GroupMember.user_id == current_user.id
        ).first()
        
        payee_membership = db.query(GroupMember).filter(
            GroupMember.group_id == settlement_data.group_id,
            GroupMember.user_id == settlement_data.payee_id
        ).first()
        
        if not payer_membership or not payee_membership:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Both users must be members of the specified group"
            )
    
    # Create the settlement
    db_settlement = Settlement(
        group_id=settlement_data.group_id,
        payer_id=current_user.id,
        payee_id=settlement_data.payee_id,
        amount=settlement_data.amount,
        description=settlement_data.description
    )
    
    db.add(db_settlement)
    db.commit()
    db.refresh(db_settlement)

    # Check if user has settled all their debts in the group after this settlement
    if settlement_data.group_id:
        calculator = BalanceCalculator(db)

        # Calculate current balances after this settlement
        balances = calculator.calculate_group_balances(db, settlement_data.group_id)
        user_balance = balances.get(current_user.id, 0)

        # If user's balance is now zero (or very close to zero), mark group as settled
        if abs(user_balance) < 0.01:
            calculator.mark_group_as_settled(db, settlement_data.group_id, current_user.id)

    return db_settlement


@router.get("/", response_model=List[SettlementResponse])
def get_settlements(
    group_id: int = Query(None, description="Filter by group ID"),
    skip: int = Query(0, ge=0, description="Number of settlements to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of settlements to return"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get settlements involving the current user.
    
    Can be filtered by group_id to show only settlements within a specific group.
    Returns settlements where the user is either the payer or payee.
    """
    query = db.query(Settlement).filter(
        (Settlement.payer_id == current_user.id) | (Settlement.payee_id == current_user.id)
    )
    
    # Filter by group if specified
    if group_id is not None:
        # Validate user is a member of the group
        membership = db.query(GroupMember).filter(
            GroupMember.group_id == group_id,
            GroupMember.user_id == current_user.id
        ).first()
        
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this group"
            )
        
        query = query.filter(Settlement.group_id == group_id)
    
    settlements = query.order_by(
        Settlement.settled_at.desc()
    ).offset(skip).limit(limit).all()
    
    return settlements


@router.get("/{settlement_id}", response_model=SettlementResponse)
def get_settlement(
    settlement_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get details of a specific settlement.
    
    User must be either the payer or payee of the settlement.
    """
    settlement = db.query(Settlement).filter(Settlement.id == settlement_id).first()
    
    if not settlement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settlement not found"
        )
    
    # Check if user is involved in the settlement
    if settlement.payer_id != current_user.id and settlement.payee_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not involved in this settlement"
        )
    
    return settlement


@router.delete("/{settlement_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_settlement(
    settlement_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a settlement record.
    
    Only the payer (person who created the settlement) can delete it.
    This might be useful if a settlement was recorded by mistake.
    """
    settlement = db.query(Settlement).filter(Settlement.id == settlement_id).first()
    
    if not settlement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settlement not found"
        )
    
    # Only the payer can delete the settlement
    if settlement.payer_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the payer can delete a settlement"
        )
    
    db.delete(settlement)
    db.commit()
