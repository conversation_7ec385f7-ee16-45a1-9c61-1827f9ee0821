# 📧 Email Configuration Setup

To enable email notifications (group invitations and password reset), you need to configure SMTP settings.

## 🔧 **Quick Setup Options**

### **Option 1: Gmail (Recommended for Testing)**

1. **Create a Gmail account** for your app (e.g., `<EMAIL>`)

2. **Enable 2-Factor Authentication** on the Gmail account

3. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Copy the 16-character password

4. **Update Email Service Configuration**:
   Edit `app/services/email_service.py`:
   ```python
   self.smtp_username = "<EMAIL>"  # Your Gmail
   self.smtp_password = "your-16-char-app-password"  # App password
   ```

### **Option 2: Professional Email Service (Production)**

For production, use a professional email service:

#### **SendGrid (Recommended)**
```python
# In email_service.py
self.smtp_server = "smtp.sendgrid.net"
self.smtp_port = 587
self.smtp_username = "apikey"
self.smtp_password = "your-sendgrid-api-key"
self.sender_email = "<EMAIL>"
```

#### **AWS SES**
```python
# In email_service.py
self.smtp_server = "email-smtp.us-east-1.amazonaws.com"
self.smtp_port = 587
self.smtp_username = "your-aws-access-key"
self.smtp_password = "your-aws-secret-key"
self.sender_email = "<EMAIL>"
```

#### **Mailgun**
```python
# In email_service.py
self.smtp_server = "smtp.mailgun.org"
self.smtp_port = 587
self.smtp_username = "<EMAIL>"
self.smtp_password = "your-mailgun-password"
self.sender_email = "<EMAIL>"
```

## 🔧 **Environment Variables (Recommended)**

Instead of hardcoding credentials, use environment variables:

1. **Update `app/config.py`**:
   ```python
   # Email settings
   smtp_server: str = "smtp.gmail.com"
   smtp_port: int = 587
   smtp_username: str = ""
   smtp_password: str = ""
   sender_email: str = "<EMAIL>"
   sender_name: str = "Bhagbanda"
   ```

2. **Update `app/services/email_service.py`**:
   ```python
   def __init__(self):
       self.smtp_server = settings.smtp_server
       self.smtp_port = settings.smtp_port
       self.smtp_username = settings.smtp_username
       self.smtp_password = settings.smtp_password
       self.sender_email = settings.sender_email
       self.sender_name = settings.sender_name
   ```

3. **Set Environment Variables**:
   ```bash
   export SMTP_USERNAME="<EMAIL>"
   export SMTP_PASSWORD="your-app-password"
   export SENDER_EMAIL="<EMAIL>"
   ```

## 🚀 **Deploy with Email Configuration**

When deploying to Google Cloud Run:

```bash
gcloud run deploy bhagbanda-admin \
  --image=your-image \
  --set-env-vars="SMTP_USERNAME=<EMAIL>,SMTP_PASSWORD=your-app-password,SENDER_EMAIL=<EMAIL>"
```

## 📧 **Email Features**

Once configured, the app will send emails for:

### **Group Invitations**
- ✅ **Sent when**: User is added to a group
- ✅ **Contains**: Group name, inviter name, app link
- ✅ **From**: `<EMAIL>`
- ✅ **Template**: Professional HTML email

### **Password Reset**
- ✅ **Sent when**: User requests password reset
- ✅ **Contains**: Secure reset link (1-hour expiry)
- ✅ **From**: `<EMAIL>`
- ✅ **Template**: Professional HTML email

## 🧪 **Testing Email Setup**

1. **Configure email credentials** as described above
2. **Restart the application**
3. **Test group invitation**:
   - Add a user to a group
   - Check if they receive an email
4. **Test password reset**:
   - Go to forgot password page
   - Enter email and submit
   - Check if reset email is received

## 🔒 **Security Notes**

- ✅ **Never commit email credentials** to version control
- ✅ **Use environment variables** for sensitive data
- ✅ **Use app passwords** instead of account passwords
- ✅ **Enable 2FA** on email accounts
- ✅ **Use professional email services** for production

## 🆘 **Troubleshooting**

### **Emails Not Sending**
1. Check SMTP credentials are correct
2. Verify app password (not account password)
3. Check firewall/network restrictions
4. Look at application logs for error messages

### **Emails Going to Spam**
1. Use a professional email service (SendGrid, etc.)
2. Set up SPF/DKIM records for your domain
3. Use a verified sender domain

### **Gmail Issues**
1. Enable 2-Factor Authentication
2. Generate new app password
3. Use the 16-character app password (no spaces)
4. Check "Less secure app access" is disabled (use app passwords instead)

## 📝 **Current Status**

**Email service is configured but needs SMTP credentials to work.**

To enable emails immediately:
1. Update `smtp_username` and `smtp_password` in `app/services/email_service.py`
2. Rebuild and deploy the container
3. Test by adding a user to a group or requesting password reset

**The email templates and sending logic are ready - just need SMTP configuration!**
