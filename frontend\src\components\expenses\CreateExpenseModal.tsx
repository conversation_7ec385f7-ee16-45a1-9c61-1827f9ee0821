/**
 * Create Expense Modal Component
 * Modal form for creating new expenses with split options
 */

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { X, Plus, Minus, Users, UserPlus } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { CreateExpenseRequest, GroupMember } from '../../types/api';
import { useAuthStore } from '../../stores/authStore';
import { userService } from '../../services/userService';

interface CreateExpenseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateExpenseRequest) => void;
  isLoading: boolean;
  groupId?: number;  // Optional for direct expenses
  groupMembers?: GroupMember[];  // Optional for direct expenses
  allowDirectExpense?: boolean;  // Whether to show direct expense option
}

export const CreateExpenseModal: React.FC<CreateExpenseModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
  groupId,
  groupMembers = [],
  allowDirectExpense = true,
}) => {
  const { user } = useAuthStore();
  const [splitType, setSplitType] = useState<'equal' | 'custom' | 'shares'>('equal');
  const [expenseType, setExpenseType] = useState<'group' | 'direct'>(groupId ? 'group' : 'direct');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<Array<{id: number, username: string, full_name?: string}>>([]);

  // Search users for direct expenses
  const { data: searchResults = [] } = useQuery({
    queryKey: ['user-search', searchTerm],
    queryFn: () => userService.searchUsers(searchTerm),
    enabled: searchTerm.length >= 2 && expenseType === 'direct',
  });

  // Auto-add current user to participants for direct expenses
  React.useEffect(() => {
    if (expenseType === 'direct' && user) {
      const currentUserInParticipants = participants?.includes(user.id.toString());
      const currentUserInSelected = selectedUsers.some(u => u.id === user.id);

      if (!currentUserInParticipants && !currentUserInSelected) {
        setValue('participants', [user.id.toString()]);
        setSelectedUsers([{
          id: user.id,
          username: user.username,
          full_name: user.full_name
        }]);
      }
    }
  }, [expenseType, user, participants, selectedUsers, setValue]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    control,
    setValue,
  } = useForm<CreateExpenseRequest & { customSplits: { user_id: number; amount_owed?: number; shares?: number }[] }>({
    defaultValues: {
      group_id: groupId,
      payer_id: user?.id,
      split_type: 'equal',
      participants: [],
      customSplits: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'customSplits',
  });

  const amount = watch('amount');
  const participants = watch('participants');

  const handleFormSubmit = (data: any) => {
    const submitData: CreateExpenseRequest = {
      group_id: expenseType === 'group' ? groupId : undefined,
      description: data.description,
      amount: parseFloat(data.amount),
      payer_id: data.payer_id,
      category: data.category,
      notes: data.notes,
      split_type: splitType,
      participants: data.participants,
    };

    if (splitType === 'custom') {
      submitData.custom_splits = data.customSplits.map((split: any) => ({
        user_id: parseInt(split.user_id),
        amount_owed: parseFloat(split.amount_owed),
      }));
    } else if (splitType === 'shares') {
      submitData.custom_splits = data.customSplits.map((split: any) => ({
        user_id: parseInt(split.user_id),
        shares: parseInt(split.shares),
      }));
    }

    onSubmit(submitData);
    reset();
  };

  // Reset selected users when switching expense types
  const handleExpenseTypeChange = (newType: 'group' | 'direct') => {
    setExpenseType(newType);
    setSelectedUsers([]);
    setValue('participants', []);
    setSearchTerm('');
  };

  const handleClose = () => {
    reset();
    setSplitType('equal');
    setSelectedUsers([]);
    setSearchTerm('');
    onClose();
  };

  const addCustomSplit = () => {
    // Only add splits for selected participants
    const selectedParticipants = participants || [];
    const existingSplitUserIds = fields.map(field => parseInt(watch(`customSplits.${fields.indexOf(field)}.user_id`)));

    // Find first participant not already in splits
    const availableParticipant = selectedParticipants.find(participantId =>
      !existingSplitUserIds.includes(parseInt(participantId))
    );

    if (availableParticipant) {
      if (splitType === 'shares') {
        append({ user_id: parseInt(availableParticipant), shares: 1 });
      } else {
        append({ user_id: parseInt(availableParticipant), amount_owed: 0 });
      }
    }
  };

  const calculateEqualSplit = () => {
    if (amount && participants.length > 0) {
      return (parseFloat(amount) / participants.length).toFixed(2);
    }
    return '0.00';
  };

  const calculateSharesAmount = (shares: number, totalShares: number) => {
    try {
      if (amount && totalShares > 0 && shares >= 0) {
        return ((parseFloat(amount) * shares) / totalShares).toFixed(2);
      }
    } catch (error) {
      console.error('Error calculating shares amount:', error);
    }
    return '0.00';
  };

  const getTotalShares = () => {
    try {
      return fields.reduce((total, field, index) => {
        const shares = watch(`customSplits.${index}.shares`);
        return total + (parseInt(shares) || 0);
      }, 0);
    } catch (error) {
      console.error('Error calculating total shares:', error);
      return 0;
    }
  };

  // Auto-populate splits when participants change
  React.useEffect(() => {
    if ((splitType === 'custom' || splitType === 'shares') && participants && participants.length > 0) {
      // Clear existing splits safely
      const currentFieldsLength = fields.length;
      for (let i = currentFieldsLength - 1; i >= 0; i--) {
        remove(i);
      }

      // Add splits for each selected participant
      participants.forEach(participantId => {
        if (splitType === 'shares') {
          append({ user_id: parseInt(participantId), shares: 1 });
        } else {
          append({ user_id: parseInt(participantId), amount_owed: 0 });
        }
      });
    } else if ((splitType === 'custom' || splitType === 'shares') && (!participants || participants.length === 0)) {
      // Clear splits when no participants selected
      const currentFieldsLength = fields.length;
      for (let i = currentFieldsLength - 1; i >= 0; i--) {
        remove(i);
      }
    }
  }, [participants, splitType]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full w-full max-w-full sm:max-w-2xl">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-screen overflow-y-auto">
            <div className="flex items-center justify-between mb-4 sticky top-0 bg-white pb-2 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Add New Expense
              </h3>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 p-2 -mr-2"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
              {/* Expense Type Selection */}
              {allowDirectExpense && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expense Type
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        value="group"
                        checked={expenseType === 'group'}
                        onChange={(e) => handleExpenseTypeChange(e.target.value as 'group' | 'direct')}
                        className="mr-3"
                        disabled={!groupId}
                      />
                      <Users className="h-5 w-5 mr-2 text-blue-500" />
                      <div>
                        <div className="font-medium">Group Expense</div>
                        <div className="text-sm text-gray-500">Split with group members</div>
                      </div>
                    </label>
                    <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        value="direct"
                        checked={expenseType === 'direct'}
                        onChange={(e) => handleExpenseTypeChange(e.target.value as 'group' | 'direct')}
                        className="mr-3"
                      />
                      <UserPlus className="h-5 w-5 mr-2 text-green-500" />
                      <div>
                        <div className="font-medium">Direct Expense</div>
                        <div className="text-sm text-gray-500">Split with specific friends</div>
                      </div>
                    </label>
                  </div>
                </div>
              )}

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description *
                </label>
                <input
                  {...register('description', { required: 'Description is required' })}
                  type="text"
                  className="input-field mt-1"
                  placeholder="e.g., Dinner at restaurant, Gas for trip"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Amount */}
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                    Amount *
                  </label>
                  <input
                    {...register('amount', { 
                      required: 'Amount is required',
                      min: { value: 0.01, message: 'Amount must be greater than 0' }
                    })}
                    type="number"
                    step="0.01"
                    className="input-field mt-1"
                    placeholder="0.00"
                  />
                  {errors.amount && (
                    <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                  )}
                </div>

                {/* Payer */}
                <div>
                  <label htmlFor="payer_id" className="block text-sm font-medium text-gray-700">
                    Paid by
                  </label>
                  <select
                    {...register('payer_id')}
                    className="input-field mt-1"
                  >
                    {expenseType === 'group' ? (
                      // Group members for group expenses
                      groupMembers.map((member) => (
                        <option key={member.user.id} value={member.user.id}>
                          {member.user.full_name || member.user.username}
                        </option>
                      ))
                    ) : (
                      // Selected users + current user for direct expenses
                      <>
                        <option value={user?.id}>
                          {user?.full_name || user?.username} (You)
                        </option>
                        {selectedUsers.filter(u => u.id !== user?.id).map((selectedUser) => (
                          <option key={selectedUser.id} value={selectedUser.id}>
                            {selectedUser.full_name || selectedUser.username}
                          </option>
                        ))}
                      </>
                    )}
                  </select>
                </div>
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category (Optional)
                </label>
                <select
                  {...register('category')}
                  className="input-field mt-1"
                >
                  <option value="">Select category</option>
                  <option value="food">Food & Dining</option>
                  <option value="transport">Transportation</option>
                  <option value="accommodation">Accommodation</option>
                  <option value="entertainment">Entertainment</option>
                  <option value="shopping">Shopping</option>
                  <option value="utilities">Utilities</option>
                  <option value="other">Other</option>
                </select>
              </div>

              {/* Split Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  How to split?
                </label>
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="equal"
                      checked={splitType === 'equal'}
                      onChange={(e) => setSplitType(e.target.value as 'equal')}
                      className="mr-2"
                    />
                    Split equally
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="custom"
                      checked={splitType === 'custom'}
                      onChange={(e) => setSplitType(e.target.value as 'custom')}
                      className="mr-2"
                    />
                    Custom amounts
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="shares"
                      checked={splitType === 'shares'}
                      onChange={(e) => setSplitType(e.target.value as 'shares')}
                      className="mr-2"
                    />
                    By shares
                  </label>
                </div>
              </div>

              {/* Participants */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Split with *
                </label>

                {expenseType === 'group' ? (
                  // Group members selection
                  <div className="space-y-2 max-h-40 sm:max-h-32 overflow-y-auto">
                    {groupMembers.map((member) => (
                      <label key={member.user.id} className="flex items-center">
                        <input
                          {...register('participants', { required: 'Select at least one participant' })}
                          type="checkbox"
                          value={member.user.id}
                          className="mr-2"
                        />
                        {member.user.full_name || member.user.username}
                        {splitType === 'equal' && participants.includes(member.user.id.toString()) && (
                          <span className="ml-auto text-sm text-gray-500">
                            ${calculateEqualSplit()}
                          </span>
                        )}
                      </label>
                    ))}
                  </div>
                ) : (
                  // Direct expense user search
                  <div>
                    <div className="mb-4">
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="input-field w-full"
                        placeholder="Search for friends to add..."
                      />

                      {searchResults.length > 0 && (
                        <div className="mt-2 border border-gray-200 rounded-md max-h-40 overflow-y-auto">
                          {searchResults.map(user => (
                            <button
                              key={user.id}
                              type="button"
                              onClick={() => {
                                const currentParticipants = participants || [];
                                if (!currentParticipants.includes(user.id.toString())) {
                                  setValue('participants', [...currentParticipants, user.id.toString()]);
                                  setSelectedUsers(prev => [...prev, user]);
                                }
                                setSearchTerm('');
                              }}
                              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center"
                            >
                              <div>
                                <div className="font-medium">{user.full_name || user.username}</div>
                                <div className="text-sm text-gray-500">@{user.username}</div>
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Selected participants for direct expenses */}
                    <div className="space-y-2">
                      {selectedUsers.map((user) => (
                        <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div>
                            <span className="font-medium">{user.full_name || user.username}</span>
                            <span className="text-sm text-gray-500 ml-2">@{user.username}</span>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newParticipants = participants.filter(id => id !== user.id.toString());
                              setValue('participants', newParticipants);
                              setSelectedUsers(prev => prev.filter(u => u.id !== user.id));
                            }}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {errors.participants && (
                  <p className="mt-1 text-sm text-red-600">{errors.participants.message}</p>
                )}
              </div>

              {/* Custom Splits */}
              {(splitType === 'custom' || splitType === 'shares') && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {splitType === 'shares' ? 'Split by Shares' : 'Custom Split Amounts'}
                    </label>
                  </div>

                  {(!participants || participants.length === 0) && (
                    <div className="mb-3 p-3 bg-yellow-50 rounded-md">
                      <p className="text-sm text-yellow-700">
                        ⚠️ Please select participants in the "Split with" section above first.
                      </p>
                    </div>
                  )}

                  {splitType === 'custom' && participants && participants.length > 0 && (
                    <div className="mb-3 p-3 bg-green-50 rounded-md">
                      <p className="text-sm text-green-700">
                        💡 <strong>Custom Amounts:</strong> Specify exactly how much each person should pay.
                        The total must equal the expense amount.
                      </p>
                    </div>
                  )}

                  {splitType === 'shares' && participants && participants.length > 0 && (
                    <div className="mb-3 p-3 bg-blue-50 rounded-md">
                      <p className="text-sm text-blue-700">
                        💡 <strong>Shares Example:</strong> If Person A gets 2 shares and Person B gets 1 share,
                        Person A pays 2/3 of the total and Person B pays 1/3.
                      </p>
                      <p className="text-xs text-blue-600 mt-1">
                        Total shares: {getTotalShares()}
                      </p>
                    </div>
                  )}

                  {participants && participants.length > 0 && (
                    <div className="space-y-3 max-h-40 sm:max-h-32 overflow-y-auto">
                      {fields.map((field, index) => (
                      <div key={field.id} className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 p-3 border border-gray-200 rounded-lg">
                        <div className="w-full sm:flex-1">
                          <label className="block text-xs font-medium text-gray-700 mb-1 sm:hidden">Person</label>
                          <select
                            {...register(`customSplits.${index}.user_id` as const)}
                            className="input-field w-full"
                          >
                            <option value="">Select person</option>
                            {expenseType === 'group' ? (
                              // Group members for group expenses
                              groupMembers
                                .filter(member => participants && participants.includes(member.user.id.toString()))
                                .map((member) => (
                                  <option key={member.user.id} value={member.user.id}>
                                    {member.user.full_name || member.user.username}
                                  </option>
                                ))
                            ) : (
                              // Selected users for direct expenses
                              selectedUsers.map((selectedUser) => (
                                <option key={selectedUser.id} value={selectedUser.id}>
                                  {selectedUser.full_name || selectedUser.username}
                                </option>
                              ))
                            )}
                          </select>
                        </div>

                        {splitType === 'shares' ? (
                          <div className="flex items-center space-x-2 w-full sm:w-auto">
                            <div className="flex-1 sm:flex-none">
                              <label className="block text-xs font-medium text-gray-700 mb-1 sm:hidden">Shares</label>
                              <input
                                {...register(`customSplits.${index}.shares` as const)}
                                type="number"
                                min="1"
                                className="input-field w-full sm:w-20"
                                placeholder="1"
                              />
                            </div>
                            <div className="flex-1 sm:flex-none">
                              <label className="block text-xs font-medium text-gray-700 mb-1 sm:hidden">Amount</label>
                              <span className="text-sm text-gray-600 font-medium block sm:inline">
                                ${(() => {
                                  try {
                                    const shares = parseInt(watch(`customSplits.${index}.shares`)) || 0;
                                    return calculateSharesAmount(shares, getTotalShares());
                                  } catch (error) {
                                    return '0.00';
                                  }
                                })()}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div className="w-full sm:w-auto">
                            <label className="block text-xs font-medium text-gray-700 mb-1 sm:hidden">Amount</label>
                            <input
                              {...register(`customSplits.${index}.amount_owed` as const)}
                              type="number"
                              step="0.01"
                              className="input-field w-full sm:w-24"
                              placeholder="0.00"
                            />
                          </div>
                        )}
                      </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Notes */}
              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes (Optional)
                </label>
                <textarea
                  {...register('notes')}
                  rows={2}
                  className="input-field mt-1 resize-none"
                  placeholder="Additional notes about this expense..."
                />
              </div>

              {/* Form Actions */}
              <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200 mt-6 sticky bottom-0 bg-white">
                <button
                  type="button"
                  onClick={handleClose}
                  className="btn-secondary w-full sm:w-auto order-2 sm:order-1"
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary w-full sm:w-auto order-1 sm:order-2"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    'Add Expense'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
