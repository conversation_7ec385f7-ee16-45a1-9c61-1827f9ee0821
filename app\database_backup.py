"""
Database backup and restore functionality for persistent storage.
This module handles backing up SQLite database to Google Cloud Storage
and restoring it on container startup.
"""

import os
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Optional

from app.config import settings


class DatabaseBackup:
    """Handles database backup and restore operations."""
    
    def __init__(self):
        self.db_path = self._get_db_path()
        self.backup_bucket = os.getenv('BACKUP_BUCKET', 'bhagbanda-db-backup')
        self.backup_filename = 'bhagbanda.db'
        
    def _get_db_path(self) -> str:
        """Get the local database file path."""
        db_url = settings.database_url
        if "sqlite" in db_url:
            # Handle different SQLite URL formats
            db_path = db_url.replace("sqlite:///", "").replace("./", "")
            
            # For container deployment, use data directory
            if not os.path.isabs(db_path):
                db_path = f"/app/data/{os.path.basename(db_path)}"
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            return db_path
        
        return "./bhagbanda.db"
    
    def backup_to_cloud(self) -> bool:
        """
        Backup database to Google Cloud Storage.
        
        Returns:
            True if backup successful, False otherwise
        """
        try:
            if not os.path.exists(self.db_path):
                print(f"⚠️  Database file not found: {self.db_path}")
                return False
            
            # Use gsutil to upload to Cloud Storage
            backup_path = f"gs://{self.backup_bucket}/{self.backup_filename}"
            timestamp_path = f"gs://{self.backup_bucket}/backups/{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.backup_filename}"

            # Upload current backup
            result1 = os.system(f'gsutil -q cp "{self.db_path}" "{backup_path}"')

            # Upload timestamped backup
            result2 = os.system(f'gsutil -q cp "{self.db_path}" "{timestamp_path}"')

            if result1 != 0 or result2 != 0:
                print(f"⚠️  Warning: Some backup uploads may have failed")
                return False
            
            print(f"✅ Database backed up to: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error backing up database: {e}")
            return False
    
    def restore_from_cloud(self) -> bool:
        """
        Restore database from Google Cloud Storage.
        
        Returns:
            True if restore successful, False otherwise
        """
        try:
            backup_path = f"gs://{self.backup_bucket}/{self.backup_filename}"
            
            # Check if backup exists
            result = os.system(f'gsutil ls "{backup_path}" > /dev/null 2>&1')
            if result != 0:
                print(f"ℹ️  No backup found at: {backup_path}")
                return False
            
            # Download backup
            result = os.system(f'gsutil -q cp "{backup_path}" "{self.db_path}" 2>/dev/null')
            if result != 0:
                print(f"❌ Failed to download backup from: {backup_path}")
                return False
            
            # Verify the downloaded file
            if os.path.exists(self.db_path):
                # Test database integrity
                conn = sqlite3.connect(self.db_path)
                conn.execute("PRAGMA integrity_check")
                conn.close()
                
                print(f"✅ Database restored from: {backup_path}")
                return True
            else:
                print(f"❌ Failed to download database backup")
                return False
                
        except Exception as e:
            print(f"❌ Error restoring database: {e}")
            return False
    
    def setup_periodic_backup(self):
        """Setup periodic backup (called from background task)."""
        import threading
        import time
        
        def backup_worker():
            while True:
                # Backup every 5 minutes
                time.sleep(300)
                self.backup_to_cloud()
        
        # Start backup thread
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        print("🔄 Periodic database backup started (every 5 minutes)")


def initialize_persistent_database():
    """
    Initialize database with persistence support.
    This function should be called during app startup.
    """
    print("🔧 Initializing persistent database...")
    
    backup_manager = DatabaseBackup()
    
    # Try to restore from cloud backup first
    if backup_manager.restore_from_cloud():
        print("📥 Database restored from cloud backup")
    else:
        print("📝 No existing backup found, will create new database")
    
    # Setup periodic backups
    backup_manager.setup_periodic_backup()
    
    return backup_manager


def backup_database_on_shutdown():
    """
    Backup database on application shutdown.
    This should be called when the app is shutting down.
    """
    print("💾 Backing up database before shutdown...")
    backup_manager = DatabaseBackup()
    backup_manager.backup_to_cloud()
