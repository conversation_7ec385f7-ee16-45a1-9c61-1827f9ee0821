"""
User model for authentication and user management.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class User(Base):
    """
    User model representing app users.
    
    Attributes:
        id: Primary key
        username: Unique username for login
        email: User's email address (unique)
        password_hash: Hashed password (never store plain text!)
        full_name: User's display name
        is_active: Whether the user account is active
        created_at: When the user account was created
        updated_at: When the user account was last updated
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    is_email_verified = Column(Boolean, default=False)
    email_verification_token = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    # Groups where this user is a member (many-to-many through GroupMember)
    group_memberships = relationship("GroupMember", back_populates="user")
    
    # Groups created by this user
    created_groups = relationship("Group", back_populates="creator")
    
    # Expenses paid by this user
    paid_expenses = relationship("Expense", back_populates="payer")
    
    # Expense splits for this user
    expense_splits = relationship("ExpenseSplit", back_populates="user")
    
    # Settlements where this user is the payer
    settlements_as_payer = relationship("Settlement", foreign_keys="Settlement.payer_id", back_populates="payer")
    
    # Settlements where this user is the payee
    settlements_as_payee = relationship("Settlement", foreign_keys="Settlement.payee_id", back_populates="payee")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
